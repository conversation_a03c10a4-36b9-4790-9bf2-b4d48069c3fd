import { ref } from 'vue';
import { defineStore } from 'pinia';
import { PROJECT_URLS } from '@repo/env-config';
import CryptoJS from 'crypto-js';
import { isArray } from 'lodash';
import { request } from '../request';

export type CommonStoreOptions = {
  key?: string;
  baseURL?: string;
  api: string;
  queryParams?: Record<string, any>;
};

export type CommonStore = {
  dataList: any[];
  initialized: boolean;
  getList: () => Promise<any[]>;
  getMap: () => Promise<Record<string, any>>;
  getOptions: () => Promise<{ label: string; value: string; raw: any }[]>;
  toDisplay: (val: number | number[]) => Promise<string>;
};

const storesMap = ref<Record<string, CommonStore>>({});

const useCommonStore = (options: CommonStoreOptions): CommonStore => {
  let key = options?.key || options.api.replace(/\//g, '_');
  key = CryptoJS.MD5(key + JSON.stringify(options.queryParams || {})).toString();

  if (storesMap.value[key]) {
    return storesMap.value[key];
  }

  const storeDefine = defineStore(key, {
    state: () => ({
      dataList: null,
      initialized: false,
    }),
    actions: {
      async getList(): Promise<any[]> {
        if (!this.initialized) {
          this.initialized = true;
          const { data } = await request(options.api, {
            params: {
              pageSize: 9999,
              ...(options.queryParams || {}),
            },
            baseURL: options.baseURL || PROJECT_URLS.MAIN_PROJECT_API,
          });

          this.dataList = data.items || data.list || data || [];
        }
        return this.dataList as any;
      },

      async getMap() {
        const rawList: any[] = (await this.getList()) as any[];

        if (!rawList) return {};

        return rawList?.reduce((acc, item) => {
          acc[item?.id] = item;
          return acc;
        }, {});
      },

      async getOptions() {
        const rawList: any[] = (await this.getList()) as any[];
        return (
          rawList?.map((item) => ({
            label: item.name,
            value: item.id,
            raw: item,
          })) || []
        );
      },

      async toDisplay(val: number | number[]) {
        const rawList: any[] = (await this.getList()) as any[];
        val = isArray(val) ? val : [val];
        const items = rawList?.filter((item) => val.includes(item.id));
        return items?.map((item) => item.name).join(', ');
      },
    },
  });

  storesMap.value[key] = storeDefine() as any;

  return storesMap.value[key];
};

export default useCommonStore;
