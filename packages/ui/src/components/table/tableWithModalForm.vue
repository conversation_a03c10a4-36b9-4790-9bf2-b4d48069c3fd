<script setup lang="ts">
  import { computed, nextTick, ref, PropType } from 'vue';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { ModalForm } from '@repo/ui/components/form';

  const props = defineProps({
    schema: {
      type: Object,
      required: true,
    },
    modalWidth: {
      type: [String, Number],
      default: 700,
    },
    moduleName: {
      type: String,
      required: true,
    },
    visibleColumns: {
      type: Array,
    },
    defaultQueryParams: {
      type: Object,
      default: () => ({}),
    },
    defaultEditValue: {
      type: Object,
      default: () => ({}),
    },
    tableActionsProperty: {
      type: Object,
      default: () => ({}),
    },
    modalFormProperty: {
      type: Object,
      default: () => ({}),
    },
    tableProperty: {
      type: Object,
      default: () => ({}),
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    data: {
      type: Array as () => Record<string, any>[],
    },
    justView: {
      type: Boolean,
      default: false,
    },
    visibleComponent: {
      type: Array as PropType<string[]>,
      default: () => ['add', 'quickSearch', 'refresh', 'layout', 'recycleBin'],
    },
    formContentRef: {
      type: Object as PropType<HTMLElement | null>,
      default: () => null,
    },
  });
  const visibleComponents = computed(() => {
    if (props.justView) return [];
    return props.visibleComponent;
  });

  const editVisible = ref(false);
  const currentEditRow = ref<any>({});
  const tableRef = ref<any>(null);
  const modalFormRef = ref<any>(null);
  const modalFormContentRef = ref<any>(null);
  const emit = defineEmits(['rowAction', 'reloadData', 'formOk', 'submit']);

  const moduleSchema = ref<any>({
    ...props.schema,
    modalEdit: true,
  });

  const handleShowEdit = (raw?: any) => {
    // eslint-disable-next-line no-alert
    editVisible.value = true;
    currentEditRow.value = {
      ...props.defaultEditValue,
      ...(raw || {}),
    };
    setTimeout(() => {
      Object.keys(props.defaultEditValue).forEach((field) => {
        modalFormRef.value.crudFormRef?.handleRecordChange(field, props.defaultEditValue[field]);
      });
    }, 1000);
  };

  const handleSubmit = () => {
    emit('submit');
  };
  const handleRowAction = (action: any, record?: any) => {
    if (action.key === 'edit' || action.key === 'add') {
      handleShowEdit(record || {});
    }
    emit('rowAction', action, record);
  };
  const handelEditVisible = () => {
    editVisible.value = true;
  };
  const handelOk = (editData: any, isEdit: boolean) => {
    editVisible.value = false;
    nextTick(() => {
      tableRef.value.loadData();
      emit('reloadData');
      emit('formOk', editData, isEdit);
    });
  };

  /**
   * 用于子组件更新当前编辑的行数据
   * @param val
   */
  const handleRecordUpdated = (val: any) => {
    currentEditRow.value = val;
  };

  defineExpose({
    loadData: async (params?: any) => {
      await tableRef.value.loadData(params);
    },
    setLoading: (loading: boolean) => {
      tableRef.value.setLoading(loading);
    },
    handleLoadData: async () => {
      await tableRef.value.handleLoadData();
    },
    getSelectedItems: () => {
      return tableRef.value.selectedItems();
    },
    setQueryParams: (params: any) => {
      tableRef.value.queryParams = params;
    },
    getQueryParams: () => {
      return tableRef.value.queryParams;
    },
    handleShowEdit,
    modalFormRef,
  });
</script>

<template>
  <a-card v-if="moduleSchema">
    <template #title>
      <div class="flex justify-between">
        <div class="flex-1">
          <slot name="title"> {{ moduleName }}</slot>
        </div>
        <slot name="action-bar"></slot>
        <table-action
          v-if="tableRef"
          :schema="moduleSchema"
          :table="tableRef"
          component-size="mini"
          v-bind="tableActionsProperty"
          :default-query-params="defaultQueryParams"
          :visible-components="visibleComponents"
          @row-action="handleRowAction"
        >
          <template #supplementary-button>
            <slot name="supplementary-button"></slot>
          </template>
        </table-action>
      </div>
    </template>
    <slot name="top-bar"></slot>
    <crud-table
      ref="tableRef"
      :schema="moduleSchema"
      :auto-load="props.autoLoad"
      :data="data"
      class="mt-2"
      :default-query-params="defaultQueryParams"
      :visible-columns="visibleColumns"
      v-bind="tableProperty"
      @row-action="handleRowAction"
    >
    </crud-table>
    <modal-form
      v-bind="modalFormProperty"
      ref="modalFormRef"
      v-model:visible="editVisible"
      v-model="currentEditRow"
      :modal-width="modalWidth"
      :schema="schema"
      :modal-name="moduleName"
      :default-ref="formContentRef"
      @submit="handleSubmit"
      @ok="handelOk"
    >
      <template #default>
        <slot name="form-content" :current-record="currentEditRow" :update-record="handleRecordUpdated"></slot>
      </template>
    </modal-form>
    <slot></slot>
  </a-card>
</template>

<style scoped lang="scss"></style>
