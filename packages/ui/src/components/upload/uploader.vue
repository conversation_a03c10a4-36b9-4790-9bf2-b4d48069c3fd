<script lang="ts" setup>
  import { computed, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { AttachmentToUpload, getOssProcessor, IAttachmentProcessor } from '@repo/infrastructure/upload';
  import { Message } from '@arco-design/web-vue';
  import { IconUpload } from '@arco-design/web-vue/es/icon';
  import AttachmentPreviewModal from '../data-display/attachmentPreviewModal.vue';
  import FileTypeIcon from '../icon/fileTypeIcon.vue';

  const props = defineProps({
    saveType: {
      type: String,
    },
    subFolder: {
      type: String,
      required: true,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    accept: {
      type: String,
      default: '*/*',
    },
    tips: {
      type: String,
      default: '点击或拖拽文件到此区域上传',
    },
    maxSize: {
      // kb
      type: Number,
      default: 1024 * 500,
    },
    minSize: {
      type: Number,
      default: 0,
    },
    modelValue: {
      type: Array,
      default: () => [],
    },
    limit: {
      type: Number,
      default: 0,
    },
    ossProcessorOptions: {
      type: Object,
    },
  });
  const emit = defineEmits(['uploaded', 'update:modelValue']);

  const uploadRef = ref<any>(null);
  const items = ref<any[]>([]);
  const inProgressItem = ref<Record<string, any>>({});
  const processor: IAttachmentProcessor = getOssProcessor(props.ossProcessorOptions);
  const { loading, setLoading } = useLoading();
  const previewVisible = ref(false);
  const openIndex = ref(0);

  const uploadedList = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  });

  const checkFileSize = (file: File) => {
    if (file.size / 1024 > props.maxSize) {
      Message.error(`文件大小不能超过 ${props.maxSize / 1024}MB`);
      return false;
    }

    if (file.size / 1024 < props.minSize) {
      Message.error(`文件大小不能小于 ${props.minSize / 1024}MB`);
      return false;
    }

    return true;
  };

  const handleUpload = async (options: any) => {
    if (!options?.fileItem?.file) return;

    setLoading(true);
    try {
      const index = options?.fileItem.uid;
      inProgressItem.value[index] = {
        coverImage: '',
        name: options.fileItem.file?.name,
        progress: 0,
      };

      const attachment: AttachmentToUpload = {
        file: options.fileItem?.file,
        saveType: props.saveType || 'attachments',
        subFolder: props.subFolder,
        handleUploadProgress: (progress) => {
          options.onProgress?.(progress);
          inProgressItem.value[index].progress = progress;
        },
        handleUploadComplete: async (url, _, uploadedResource) => {
          options.onSuccess?.(url);
          items.value.push({
            url,
            id: uploadedResource?.id || null,
            name: options.fileItem.file?.name,
          });
          delete inProgressItem.value[index];
          if (Object.values(inProgressItem.value).length === 0) {
            emit('uploaded', items.value);
            uploadedList.value = [...uploadedList.value, ...items.value];
            items.value = [];
          }
        },
      };

      await processor.uploadLarge(attachment);
    } finally {
      // emit('uploaded');
      setLoading(false);
    }
  };

  const handlePreview = (index: number) => {
    openIndex.value = index;
    previewVisible.value = true;
  };

  const handleDelete = (index: number) => {
    const raw = uploadedList.value;
    raw.splice(index, 1);
    uploadedList.value = [...raw];
  };

  defineExpose({
    items,
    processor,
  });
</script>

<template>
  <a-upload
    ref="uploadRef"
    :accept="accept"
    :loading="loading"
    :custom-request="handleUpload as any"
    :on-before-upload="checkFileSize"
    :tip="tips"
    :multiple="multiple"
    :file-list="uploadedList"
    v-bind="{
      draggable: true,
      ...$attrs,
    }"
    :limit="limit"
    :show-remove-button="false"
  >
    <template #upload-button>
      <slot name="uploadButton">
        <div class="border-2 border-dashed rounded-xl w-full p-8 hover:border-blue-400 cursor-pointer">
          <div class="flex flex-col items-center justify-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <IconUpload class="text-2xl text-blue-600" />
            </div>
            <div class="text-sm text-gray-500">拖拽文件到此处或点击选择文件</div>
          </div>
        </div>
      </slot>
    </template>

    <template #upload-item="{ fileItem, index }">
      <div :key="fileItem.uid" class="upload-file-item flex justify-between gap-2">
        <div
          :class="{ finished: fileItem.percent >= 1 }"
          :style="{ width: `${fileItem.percent * 100}%` }"
          class="progress-bar"
        ></div>
        <a-space
          class="name cursor-pointer element"
          :class="{ 'text-blue-700 font-medium': fileItem.percent >= 1 }"
          @click="() => handlePreview(index)"
        >
          <file-type-icon :file="fileItem.name" :thumb-size="{ height: 20 }" :url="fileItem.url || fileItem.udf1" />
          <div>{{ fileItem.name }}</div>
        </a-space>
        <div class="percentage element">
          <div v-if="fileItem.percent === 0"> 正在准备上传...</div>
          <div v-else-if="fileItem.percent < 1">已上传 {{ (fileItem.percent * 100).toFixed(2) }}% </div>
          <div v-else class="finished-text">
            <IconCheck />
            上传完成
          </div>
        </div>
        <a-popconfirm v-if="!$attrs.disabled" content="确定要删除吗？" @ok="() => handleDelete(index)">
          <a-button v-if="fileItem.percent >= 1" class="element" size="mini" status="danger" type="outline">
            <template #icon>
              <IconDelete />
            </template>
          </a-button>
        </a-popconfirm>
      </div>
    </template>
  </a-upload>

  <attachment-preview-modal v-model="previewVisible" :current-file-index="openIndex" :files-list="uploadedList" />
</template>

<style lang="less" scoped>
  .upload-file-item {
    position: relative;
    padding: 8px;
    font-size: 12px;
    background: #f2f2f2;
    border-radius: 4px;
    margin: 8px 0;
    align-items: center;

    .element {
      position: relative;
      z-index: 2;
    }

    .name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .percentage {
      margin-left: 16px;
    }

    .finished-text {
      color: var(--success-1);
    }

    .progress-bar {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      z-index: 1;
      border-radius: 4px;
      background: rgba(var(--primary-4), 0.3);
      max-width: 100%;

      &.finished {
        background: rgba(var(--success-3), 0.4);
      }
    }
  }
</style>
