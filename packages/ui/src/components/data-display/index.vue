<template>
  <component
    :is="displayComponent"
    v-if="displayComponent && ready"
    v-model:raw="rawValue"
    v-model:record="recordData"
    :value="rawValue"
    :string-filters="stringFilters"
    :schema-field="schemaField"
    v-bind="{ ...(schemaField?.displayProps || {}), ...(schemaField?.listProps?.componentProps || {}) }"
    @refresh="handleRefresh"
  />
  <span v-else-if="displayValue !== undefined" :class="`${schemaField.key}-display`" v-html="displayValue" />
</template>

<script setup lang="ts">
  import { computed, onMounted, PropType, ref, toRaw, watch } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { cloneDeep, isString } from 'lodash';
  import { computedAsync } from '@vueuse/core';
  import displayComponents from './components';

  const props = defineProps({
    schemaField: {
      type: Object as PropType<any>,
      required: true,
    },
    record: {
      type: Object as PropType<Record<string, any>>,
      default: null,
    },
    stringFilters: {
      type: Object as PropType<Record<string, string[]>>,
      default: () => ({}),
    },
  });

  const ready = ref<any>(false);
  const emit = defineEmits(['refresh', 'update:record']);

  const rawValue = computed({
    get: () => {
      if (props.schemaField.key?.indexOf('.') > 0) {
        return SchemaHelper.toDeepValue(props.record, props.schemaField.key!);
      }
      return props.record?.[props.schemaField.key!];
    },
    set: (val) => {
      const rawRecord = props.record;
      if (props.schemaField.key?.indexOf('.') > 0) {
        SchemaHelper.setDeepValue(rawRecord, props.schemaField.key!, val);
      } else {
        rawRecord[props.schemaField.key!] = val;
      }
      emit('update:record', props.record);
    },
  });

  const recordData = computed({
    get: () => {
      return props.record;
    },
    set: (val) => {
      emit('update:record', val);
    },
  });

  const handleRefresh = () => {
    emit('refresh');
  };

  // const displayValue = computedAsync(async () => {
  //   // check if there's toDisplay function in schemaField first
  //   const raw = cloneDeep(rawValue.value);
  //   if (typeof props.schemaField?.displayProps?.toDisplay === 'function') {
  //     const toDisplayValue = await props.schemaField.displayProps?.toDisplay(raw, props.record);
  //     // if toDisplay return a Component
  //     if (!(toDisplayValue instanceof Object)) {
  //       return toDisplayValue;
  //     }
  //   }
  //
  //   // static select
  //   if (props.schemaField.inputWidgetProps?.options) {
  //     const option = props.schemaField?.inputWidgetProps?.options?.find((o: any) => {
  //       return o.value === props.record?.[props.schemaField.key!] || o.id === props.record?.[props.schemaField.key!];
  //     });
  //
  //     const res = option?.label || option?.name;
  //     if (res?.trim()) {
  //       return res;
  //     }
  //   }
  //
  //   const key = props.schemaField.key!;
  //   let displayString = props.record?.[key];
  //   if (props.record) {
  //     displayString = SchemaHelper.toDeepValue(props.record, props.schemaField.key!);
  //   }
  //   if (props.stringFilters[key] && displayString) {
  //     displayString = displayString.replace(
  //       new RegExp(props.stringFilters[key].join('|'), 'gi'),
  //       (matched: any) => `<span class="in-cell-highlight">${matched}</span>`,
  //     );
  //   }
  //   /* 小周 */
  //   if (props.record?.[key] instanceof Object) {
  //     displayString = props.record?.[key]?.name;
  //   }
  //   return displayString;
  // });

  const displayValue = ref<any>(undefined);

  watch(
    () => rawValue.value,
    async (val) => {
      // check if there's toDisplay function in schemaField first
      const raw = cloneDeep(val);
      if (typeof props.schemaField?.displayProps?.toDisplay === 'function') {
        const toDisplayValue = await props.schemaField.displayProps?.toDisplay(raw, props.record);
        // if toDisplay return a Component
        if (!(toDisplayValue instanceof Object)) {
          displayValue.value = toDisplayValue;
          return;
        }
      }

      // static select
      if (props.schemaField.inputWidgetProps?.options) {
        const option = props.schemaField?.inputWidgetProps?.options?.find((o: any) => {
          return o.value === props.record?.[props.schemaField.key!] || o.id === props.record?.[props.schemaField.key!];
        });

        const res = option?.label || option?.name;
        if (res?.trim()) {
          displayValue.value = res;
          return;
        }
      }

      const key = props.schemaField.key!;
      let displayString = props.record?.[key];
      if (props.record) {
        displayString = SchemaHelper.toDeepValue(props.record, props.schemaField.key!);
      }
      if (props.stringFilters[key] && displayString) {
        displayString = displayString.replace(
          new RegExp(props.stringFilters[key].join('|'), 'gi'),
          (matched: any) => `<span class="in-cell-highlight">${matched}</span>`,
        );
      }
      /* 小周 */
      if (props.record?.[key] instanceof Object) {
        displayString = props.record?.[key]?.name;
      }
      displayValue.value = displayString;
    },
    { immediate: true, deep: true },
  );

  const displayComponent = computed(() => {
    // 优先使用 listProps.component（用于表格列表显示）
    if (props.schemaField.listProps?.component) {
      if (isString(props.schemaField.listProps.component)) {
        const ucFirstName =
          props.schemaField.listProps.component.charAt(0).toUpperCase() +
          props.schemaField.listProps.component.slice(1);
        return displayComponents[ucFirstName];
      }
      return props.schemaField.listProps.component;
    }
    // 其次使用 displayProps.component（用于详情页显示）
    if (props.schemaField.displayProps?.component) {
      if (isString(props.schemaField.displayProps.component)) {
        const ucFirstName =
          props.schemaField.displayProps.component.charAt(0).toUpperCase() +
          props.schemaField.displayProps.component.slice(1);
        return displayComponents[ucFirstName];
      }
      return props.schemaField.displayProps.component;
    }
    if (displayComponents[`${props.schemaField?.valueType}Display`]) {
      return displayComponents[`${props.schemaField?.valueType}Display`];
    }

    if (typeof props.schemaField.displayProps?.toDisplay === 'object') {
      return props.schemaField.displayProps?.toDisplay;
    }
    return null;
  });

  onMounted(() => {
    ready.value = true;
  });
</script>

<script lang="ts">
  export default {
    name: 'DataDisplay',
  };
</script>

<style scoped></style>
