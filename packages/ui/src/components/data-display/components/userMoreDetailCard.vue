<script setup lang="ts">
  import { onMounted, PropType, ref, computed } from 'vue';
  import { IconUser, IconFile, IconCheckCircle, IconIdcard } from '@arco-design/web-vue/es/icon';
  import useCommonStore from '@repo/infrastructure/utils/store';

  const props = defineProps({
    teacher: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  });
  const defaultCategory = ['教师资格证', '康复证书', '送教承诺书'];

  const sysRoleStore = useCommonStore({
    api: '/org/sysRole',
  });
  const store = useCommonStore({
    api: '/org/userTitle',
  });

  const roleList = ref([]);
  const userTitleList = ref([]);
  const getRole = (value) => {
    return roleList.value
      .filter((item) => value?.indexOf(item.id) >= 0)
      .map((item) => {
        return item.name;
      })
      .join('、');
  };

  const getUserTitle = (value) => {
    return userTitleList.value
      .filter((item) => value?.indexOf(item.id) >= 0)
      .map((item) => {
        return item.name;
      })
      .join('、');
  };

  const baseInfo = computed(() => [
    { label: '学校', value: props.teacher?.branchOffice?.name },
    { label: '用户名', value: props.teacher?.username },
    { label: '手机', value: props.teacher?.mobile },
    { label: '职位', value: getRole(props.teacher?.sysRoleSet || []) },
    { label: '角色', value: getUserTitle(props.teacher?.userTitleIds || []) },
    { label: '身份证', value: props.teacher?.idCard },
  ]);

  const progress = computed(() => {
    return (((props.teacher?.itemAttachments?.length || 0) / defaultCategory.length) * 100).toFixed(0);
  });
  onMounted(async () => {
    const [roleListData, userTitleListData] = await Promise.all([sysRoleStore.getList(), store.getList()]);
    roleList.value = roleListData;
    userTitleList.value = userTitleListData;
  });
</script>

<template>
  <div class="w-full bg-white shadow-sm overflow-hidden">
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-6 md:px-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <!-- 用户头像 -->
          <a-avatar
            :size="60"
            class="bg-white/50 text-purple-600 shadow-lg shadow-white/50"
            :image-url="teacher?.avatar"
          >
            <icon-user />
          </a-avatar>

          <!-- 用户信息 -->
          <div class="text-white">
            <h2 class="text-xl font-semibold mb-1">{{ teacher?.name }}</h2>
            <p class="text-purple-100 text-sm">{{ teacher?.teacherType }}</p>
          </div>
        </div>

        <!-- 状态标签 -->
        <div class="flex space-x-2 p-2 bg-white/20 rounded-full">
          <div
            v-if="teacher?.validated"
            :class="[
              'text-xs flex justify-center space-x-2 items-center border border-white/40 px-3 ',
              'rounded-full font-bold text-white  bg-white/20 shadow-sm shadow-white/20 select-none',
            ]"
          >
            <IconCheckCircle />
            <span>已审核</span>
          </div>
          <div
            :class="[
              'text-xs flex justify-center space-x-2 items-center border  px-3 py-[2px]',
              'rounded-full font-bold  shadow-sm  select-none',
              teacher?.inPosition
                ? 'border-green-200/40 text-green-400 bg-green-100/30 shadow-green-100/20'
                : 'border-red-200/40 text-red-500 bg-red-100/30 shadow-red-100/20',
            ]"
          >
            <IconCheckCircle />
            <span>{{ teacher?.inPosition ? '在职' : '离职' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 基本信息区域 -->
    <div class="p-4 md:p-6">
      <div class="mb-6">
        <div class="flex items-center mb-4">
          <IconUser class="text-gray-500 mr-2" />
          <h3 class="text-lg font-medium text-gray-800">基本信息</h3>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 bg-gray-50 rounded-lg border">
          <div v-for="(item, index) in baseInfo" :key="index" class="p-4">
            <p class="text-gray-500 text-xs mb-1">{{ item.label }}</p>
            <p class="text-gray-900 font-semibold text-sm break-words">{{ item.value || '-' }}</p>
          </div>
        </div>
      </div>

      <!-- 教师信息区域 -->
      <div class="mb-6">
        <div class="flex items-center mb-4">
          <IconIdcard class="text-gray-500 mr-2" />
          <h3 class="text-lg font-medium text-gray-800">教师信息</h3>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="bg-orange-100 p-4 rounded-lg border border-orange-300">
            <div class="flex items-center mb-3">
              <div class="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
              <h4 class="font-medium text-orange-800">学历资质</h4>
            </div>
            <div class="space-y-2 text-sm">
              <div>
                <span class="text-gray-600">学历：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.highestEducation }}</span>
              </div>
              <div>
                <span class="text-gray-600">职称：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.proTitle }}</span>
              </div>
              <div>
                <span class="text-gray-600">专业：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.teachType }}</span>
              </div>
            </div>
          </div>

          <div class="bg-blue-50 p-4 rounded-lg border border-blue-300">
            <div class="flex items-center mb-3">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
              <h4 class="font-medium text-blue-800">教学信息</h4>
            </div>
            <div class="space-y-2 text-sm">
              <div>
                <span class="text-gray-600">类别：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.teacherType ?? '-' }}</span>
              </div>
              <div>
                <span class="text-gray-600">学段：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.teachGrade }}</span>
              </div>
              <div>
                <span class="text-gray-600">科目：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.teachingSubjects?.join('、') }}</span>
              </div>
            </div>
          </div>

          <!-- 职务信息 -->
          <div class="bg-green-50 p-4 rounded-lg border border-green-300">
            <div class="flex items-center mb-3">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <h4 class="font-medium text-green-800">职务信息</h4>
            </div>
            <div class="space-y-2 text-sm">
              <div>
                <span class="text-gray-600">编制：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.establishment }}</span>
              </div>
              <div>
                <span class="text-gray-600">类别：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.teachType }}</span>
              </div>
              <div>
                <span class="text-gray-600">职务：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.title }}</span>
              </div>
            </div>
          </div>

          <!-- 时间信息 -->
          <div class="bg-purple-50 p-4 rounded-lg border border-purple-300">
            <div class="flex items-center mb-3">
              <div class="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
              <h4 class="font-medium text-purple-800">时间信息</h4>
            </div>
            <div class="space-y-2 text-sm">
              <div>
                <span class="text-gray-600">参加工作：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.dateOfWorkStarting }}</span>
              </div>
              <div>
                <span class="text-gray-600">档案时间：</span>
                <span class="text-gray-800 text-xs">{{ teacher?.dateOfSpecialEduStarting }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 证件资料区域 -->
      <div>
        <div class="flex items-center mb-4">
          <IconFile class="text-gray-500 mr-2" />
          <h3 class="text-lg font-medium text-gray-800">证件资料</h3>
        </div>

        <div v-if="teacher?.itemAttachments?.length" class="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div
            v-for="(cert, index) in teacher?.itemAttachments"
            :key="index"
            class="flex flex-col items-center transition-colors"
          >
            <!-- 证书图标 -->
            <div
              class="w-12 h-12 mb-2 flex items-center justify-center rounded-lg"
              :class="{
                'text-blue-600 ': index === 0,
                'text-green-600 ': index === 1,
                'text-purple-600 ': index === 2,
                'text-orange-600 ': index === 3,
                'text-red-600': index === 4,
              }"
            >
              <IconFile :size="24" />
            </div>

            <p class="text-sm font-medium text-gray-800 text-center mb-1">{{ cert.category }}</p>

            <!--<a-tag v-if="cert.verified" color="green" size="small" class="text-xs">
              <template #icon>
                <IconCheckCircle />
              </template>
              已审核
            </a-tag>
            <a-tag v-else color="orange" size="small" class="text-xs"> 待审核</a-tag>-->
          </div>
        </div>
        <a-empty v-else />

        <div
          class="mt-4 p-3 rounded flex items-center bg-gradient-to-r"
          :class="progress < 67 ? 'from-white to-red-100  text-red-700 ' : 'from-white to-green-100  text-green-700 '"
        >
          <IconCheckCircle class="mr-2" />
          <div class="text-sm">
            <span class="font-medium"> 证件完整度： {{ progress }}% </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
  :deep(.arco-tag) {
    display: inline-flex;
    align-items: center;
  }

  :deep(.arco-avatar) {
    border: 2px solid rgba(255, 255, 255, 0.8);
  }

  @media (max-width: 640px) {
    :deep(.arco-tag) {
      padding: 0 6px;
    }
  }
</style>
