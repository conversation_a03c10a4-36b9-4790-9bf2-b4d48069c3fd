import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';
import { schoolTypes } from '@repo/infrastructure/data';
import { institutionTypes, normalSchoolTypes, vocationalSchoolTypes } from '@repo/infrastructure/data/schoolTypes';
import { getAdminClientNature } from '../src/utils/utils';

const getDisplayName = (enrollmentYear, gradeName) => {
  const res = [];
  if (enrollmentYear) {
    res.push(`[${enrollmentYear}届]`);
  }
  if (gradeName) {
    res.push(gradeName ?? '');
  }
  return res.join('');
};

const gradesMap = {
  primarySchool: ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级'],
  middleSchool: ['七年级', '八年级', '九年级'],
  highSchool: ['高一', '高二', '高三'],
};

const getGrades = (type: string) => {
  const grades = [];
  switch (type) {
    case '小学':
      grades.push(...gradesMap.primarySchool);
      break;
    case '九年一贯制':
      grades.push(...gradesMap.primarySchool, ...gradesMap.middleSchool);
      break;
    case '十二年一贯制':
      grades.push(...gradesMap.primarySchool, ...gradesMap.middleSchool, ...gradesMap.highSchool);
      break;
    case '初中':
      grades.push(...gradesMap.middleSchool);
      break;
    case '高中':
      grades.push(...gradesMap.highSchool);
      break;
    default:
      break;
  }
  return grades;
};

const grade: CustomSchema = {
  api: '/teacher/schoolGrade',
  // rowActions: [{ key: 'periods', label: '学期管理', handler() {} }],
  formViewProps: {},
  fieldsMap: {
    school: {
      dataType: 'Foreign',
      foreignField: {
        api: '/resourceCenter/fusionSchool',
        preload: true,
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        queryParams: async () => {
          const nature = getAdminClientNature();
          return {
            nature,
          };
        },
      },
      inputWidgetProps: {
        allowSearch: true,
        valueType: (value, computedOptions) => {
          const school = computedOptions.find((item) => item.raw?.id === value);
          return {
            id: school.value,
            name: school.label,
            type: school.raw.type,
          };
        },
        onValueChange: (val: any, record: any) => {
          let gradeName = '一年级';
          let graduateYear = 0;
          switch (record.value.school.type) {
            case '小学':
              graduateYear = 6;
              break;
            case '九年一贯制':
              graduateYear = 9;
              break;
            case '十二年一贯制':
              graduateYear = 12;
              break;
            case '初中':
              gradeName = '七年级';
              graduateYear = 3;
              break;
            case '高中':
              gradeName = '高一';
              graduateYear = 3;
              break;
            default:
              gradeName = null;
              break;
          }
          record.value.name = `[${record.value.enrollmentYear}届]${gradeName}`;
          record.value.graduateYear = record.value.enrollmentYear + graduateYear;
          record.value.gradeName = gradeName;
        },
      },
    },
    name: {
      inputWidgetProps: {
        readonly: true,
        queryDepends: {
          onGradeNameChange(gradeName, inputValue, formData) {
            inputValue.value = getDisplayName(formData.value.enrollmentYear, formData.value.gradeName);
            return {};
          },
          onEnrollmentYearChange(gradeName, inputValue, formData) {
            inputValue.value = getDisplayName(formData.value.enrollmentYear, formData.value.gradeName);
            return {};
          },
        },
      },
    },
    gradeName: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        reloadOptionOnFocus: true,
        getOptions: (record: any) => {
          const grades = getGrades(record?.school?.type);
          return grades.map((g) => ({ label: g, value: g }));
        },
        onValueChange: (val: string, record: any) => {
          const grades = getGrades(record?.value?.school?.type);
          record.value.graduateYear = record.value.enrollmentYear + (grades.length - grades.indexOf(val));
        },
      },
    },
    enrollmentYear: {
      listProps: {
        columnWidth: 100,
      },
      defaultValue: () => {
        const date = new Date(); // 8/1
        return date.getFullYear();
      },
    },
    graduateYear: {
      listProps: {
        columnWidth: 100,
      },
    },
    currentPeriod: {
      visibleInForm: false,
      displayProps: {
        detailSpan: 2,
        toDisplay(value) {
          return value?.name || '无法确定当前学期';
        },
      },
    },
    gradePeriods: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
      displayProps: {
        detailSpan: 2,
        component: defineAsyncComponent(() => import('../src/teacher/schoolGradePeriodsDisplay.vue')),
      },
    },
    orgNature: {
      defaultValue: getAdminClientNature,
      visibleInForm: false,
    },
  },
};

export default { grade };
