<script lang="ts" setup>
  import { useUserStore } from '@repo/infrastructure/store';
  import { ref, onMounted, computed, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  import studentDetailButton from '@repo/components/student/studentDetailButton.vue';
  import AvatarDisplay from '@repo/ui/components/data-display/components/avatarDisplay.vue';
  import { getCurrentPeriod } from '@repo/infrastructure/utils/scaffolds';
  import PhotoList from './photoList.vue';
  import ViewModal from './viewModal.vue';
  import D2dShareModal from './d2dShareModal.vue';
  import SendRecordViewComponent from './SendRecordViewComponent.vue';
  import TeachingPlanModal from './teachingPlanModal.vue';
  import Analysis from '../assessment/analysis.vue';

  const props = defineProps({
    shareLinkInfo: {
      type: Object,
    },
  });
  const userStore = useUserStore();
  const schoolList = ref<any>([]);
  const gradClassList = ref<any>([]);
  const searchData = ref<any>(null);
  const filterSearchData = ref<any>(null);
  const currentIndexCard = ref({ index: 0, key: 'teachersCount' });
  const size = 'mini';
  const period = ref<any>([]);

  const filterMenu = ref<any>([
    { label: '所属学校', options: [], span: 5, value: null },
    { label: '班级分组', options: gradClassList.value, span: 5, value: null },
    { label: '学期', options: period.value, span: 4, value: -1 },
    {
      label: '状态',
      options: [
        { label: '已完成', value: 1 },
        { label: '进行中', value: 0 },
      ],
      span: 3,
      value: null,
    },
  ]);

  const briefInfo = ref([
    { label: '送教教师', key: 'teachersCount', unit: '人' },
    { label: '送教学生', key: 'studentsCount', unit: '人' },
    { label: '送教学校', key: 'schoolsCount', unit: '所' },
    { label: '送教次数', key: 'sendTimesCount', unit: '次' },
    { label: '送教照片', key: 'sendPhotosCount', unit: '张' },
  ]);
  const baseColumns = ref([
    { title: '姓名/名称', dataIndex: 'name', slotName: 'studentName' },
    { title: '老师名字', dataIndex: 'teacherName', slotName: 'teacherName' },
    { title: '所属学校', dataIndex: 'schoolName', slotName: 'schoolName' },
    { title: '送教次数', dataIndex: 'sendTimes', slotName: 'sendTimes' },
    { title: '送教教师', dataIndex: 'teachersCount', slotName: 'teachersCount' }, // 数量

    { title: '性别', dataIndex: 'gender', slotName: 'gender' },
    { title: '障碍类型', dataIndex: 'disorders', slotName: 'disorders' },

    { title: '送教人数', dataIndex: 'teacherOrStudentNum', slotName: 'teacherOrStudentNum' },

    { title: '送教日期', dataIndex: 'date', slotName: 'date' },
    { title: '送教教师', dataIndex: 'sendTeachers', slotName: 'sendTeachers' }, // teacher
    { title: '现场照片', dataIndex: 'attachmentsList', slotName: 'attachmentsList' }, // 应该只有插槽
    { title: '课时', dataIndex: 'classHour', slotName: 'classHour' },

    { title: '送教记录', slotName: 'sendRecord', align: 'center' }, // 应该只有插槽
    { title: '操作', slotName: 'operation' },
  ]);

  const searchParams = ref({
    boId: null, // userStore.branchOffice.id
    finished: null,
    page: 1,
    pageSize: 15,
    sharedUserId: null,
    topBoId: userStore.company.id,
    period: null,
    type: 'teachersCount',
    ...(props.shareLinkInfo?.configs || {}),
  });
  const shareLinkInfo = ref({
    ...props.shareLinkInfo,
  });

  const columns = ref([...baseColumns.value]);

  const search = async () => {
    await request('resourceRoom/sendRecordStatistics/search', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'post',
      data: searchParams.value,
    }).then((res: any) => {
      searchData.value = res.data;
      filterSearchData.value = JSON.parse(JSON.stringify(res.data));
    });
  };
  const searchText = ref();
  const handleFilter = (val): any => {
    let filed = 'studentName';
    if (currentIndexCard.value.key === 'teachersCount') {
      filed = 'name';
    } else if (currentIndexCard.value.key === 'schoolsCount') {
      filed = 'schoolName';
    } else filed = 'name';

    filterSearchData.value.lists = searchData.value?.lists.filter((item) => val === item?.[filed]);
  };
  const reset = () => {
    searchText.value = null;
    filterSearchData.value = JSON.parse(JSON.stringify(searchData.value));
  };

  const loadData = async () => {
    const loadBoTree = await request('/statistics/d2dEducationShare/filtersItems', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'post',
      data: searchParams.value,
    }).then((res) => {
      schoolList.value = res.data.boTree;
    });
    await Promise.all([loadBoTree, search()]);
    // await search();
  };
  const loadGradClass = async () => {
    await request('/resourceRoom/gradeClass', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        boId: searchParams.value.boId,
      },
    }).then((res) => {
      gradClassList.value = [];
      res.data.items.forEach((item: any) => {
        gradClassList.value.push({
          label: item.name,
          value: item.id,
        });
      });
      filterMenu.value.find((item) => item.label === '班级分组').options = gradClassList.value;
    });
  };
  const handelTreeChange = async (val: any) => {
    searchParams.value.boId = val.value;
    await loadGradClass();
  };
  const handleClickCard = async (item: any, index) => {
    currentIndexCard.value.index = index;
    currentIndexCard.value.key = item.key;
    searchParams.value.type = item.key;
    await search();
  };

  const ready = ref<any>(false);
  const filteredColumns = computed(() => {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    ready.value = false;
    const cl = columns.value.filter((column: any) => {
      if (!(currentIndexCard.value.key === 'sendTimesCount') && column.dataIndex === 'classHour') {
        return false;
      }
      if (
        currentIndexCard.value.key === 'sendTimesCount' &&
        (column.dataIndex === 'sendTimes' || column.dataIndex === 'teacherOrStudentNum')
      )
        return false;
      if (!(currentIndexCard.value.key === 'sendPhotosCount') && column.dataIndex === 'attachmentsList') return false;
      if (currentIndexCard.value.key === 'teachersCount' && column.dataIndex === 'teacherOrStudentNum')
        column.title = '送教学生';
      else if (column.dataIndex === 'teacherOrStudentNum') column.title = '送教老师';
      // additional filter condition
      if (currentIndexCard.value.key === 'schoolsCount' && column.dataIndex === 'teacherOrStudentNum')
        column.title = '送教学生';

      return (
        (searchData.value?.lists.length > 0 &&
          Object.prototype.hasOwnProperty.call(searchData.value.lists[0], column.dataIndex)) ||
        (column?.slotName === 'operation' &&
          !['studentsCount', 'sendPhotosCount'].includes(currentIndexCard.value.key)) ||
        (currentIndexCard.value.key === 'studentsCount' && ['sendRecord'].includes(column?.slotName))
      );
    });
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    ready.value = true;
    return cl;
  });

  const currentSelectType = ref<string>('');
  const setCurrentSelectType = (type: string) => {
    currentSelectType.value = type;
  };

  const handleChange = (val: any) => {
    switch (currentSelectType.value) {
      case filterMenu.value[1].label:
        if (val === -1) delete searchParams.value.gradeClassId;
        else searchParams.value.gradeClassId = val;
        break;

      case filterMenu.value[2].label:
        if (val === -1) delete searchParams.value.period;
        else searchParams.value.period = val;
        break;

      case filterMenu.value[3].label:
        if (val === -1) delete searchParams.value.finished;
        else searchParams.value.finished = val;
        break;
      case 'month':
        if (val === -1) delete searchParams.value.month;
        else searchParams.value.month = val;
        break;

      default:
        currentSelectType.value = '';
        break;
    }
  };
  const viewModalVisible = ref(false);
  const viewList = ref([]);

  const drawerVisible = ref(false);

  const currentRecord = ref();
  const handleView = async (record: any, type: string) => {
    currentRecord.value = record;
    switch (type) {
      case 'operation': {
        break;
      }
      case 'sendRecord':
        break;
      default:
        break;
    }
    drawerVisible.value = true;
  };

  const loadCount = async () => {
    await request('/statistics/d2dEducationShare/getPeriod', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    }).then((res: any) => {
      res.data.forEach((item: any) => {
        period.value.push({
          label: item,
          value: item,
        });
      });
    });
  };
  const shareAll = ref(false);
  const shareModalVisible = ref(false);
  const handleShare = (val) => {
    shareAll.value = val;
    shareModalVisible.value = true;
  };
  const isShare = ref(false);

  const teachingPlanVisible = ref(false);
  const teachingPlanType = ref(null);

  const planId = ref(null);
  const handleShowPlanModal = (type: string, record: number[]) => {
    teachingPlanType.value = type;
    planId.value = record?.[0];
    teachingPlanVisible.value = true;
  };

  const currentAssess = ref(null);
  const analysisVisible = ref(false);

  const handleViewAssess = (item: any) => {
    currentAssess.value = item;
    analysisVisible.value = true;
  };

  onMounted(async () => {
    // await loadData();
    // await loadCount();
    await Promise.all([loadData(), loadCount()]);
    if (props.shareLinkInfo?.id) isShare.value = true;
  });
  watch(
    () => props.shareLinkInfo?.id,
    (newVal) => {
      if (newVal) isShare.value = true;
    },
  );
</script>

<template>
  <!--  {{ filterSearchData }}-->
  <!--  {{ searchData }}-->
  <div class="p-5 w-5/6 mx-auto">
    <a-card :bordered="false" class="rounded-md">
      <template #title>
        <span>{{ (isShare ? '' : userStore.company.name) + '送教信息统计' }}</span>
      </template>
      <div>
        <a-form auto-label-width>
          <a-row class="flex justify-around">
            <a-col v-for="(item, index) in filterMenu" :key="index" :span="item.span">
              <a-form-item :label="item.label">
                <a-tree-select
                  v-if="item.label === '所属学校'"
                  :disabled="isShare"
                  :size="size"
                  :data="schoolList"
                  :label-in-value="true"
                  :field-names="{
                    key: 'id',
                    title: 'name',
                    children: 'children',
                  }"
                  :placeholder="item.label"
                  allow-search
                  @change="handelTreeChange"
                ></a-tree-select>
                <a-select
                  v-else
                  v-model="item.value"
                  :size="size"
                  :options="item.options"
                  :placeholder="item.label"
                  @click="setCurrentSelectType(item.label)"
                  @change="handleChange"
                >
                  <a-option :value="-1">全部</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="">
                <a-button :size="size" class="ml-2" type="primary" @click="search">
                  <icon-filter />
                  筛选
                </a-button>

                <a-dropdown v-if="!isShare" trigger="hover">
                  <a-button :size="size" class="ml-2" status="success" type="outline">
                    <icon-share-alt />
                    分享
                  </a-button>
                  <template #content>
                    <a-doption @click="handleShare(false)">仅筛选</a-doption>
                    <a-doption @click="handleShare(true)">全部</a-doption>
                  </template>
                </a-dropdown>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false" class="custom-card">
      <div class="custom-flex">
        <div
          v-for="(item, index) in briefInfo"
          :key="index"
          :class="currentIndexCard.index === index ? 'active-card' : ''"
          class="custom-info-card"
          @click="handleClickCard(item, index)"
        >
          <div>{{ item.label }}</div>
          <div class="custom-info-value">
            <span class="custom-info-number">{{ searchData?.[item.key] ?? 0 }}{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </a-card>
    <a-card :bordered="false" class="rounded-md mt-3">
      <div class="mb-4 flex justify-end">
        <a-input-search
          v-model="searchText"
          :style="{ width: '320px' }"
          placeholder="输入名称/姓名"
          button-text="搜索"
          search-button
          :size="size"
          class="mr-2"
          @search="handleFilter"
        />
        <a-button type="outline" :size="size" @click="reset">
          <icon-refresh />
          重置
        </a-button>
      </div>
      <a-table
        v-if="currentIndexCard.key !== 'sendPhotosCount'"
        :columns="filteredColumns"
        :data="filterSearchData?.lists"
      >
        <template #studentName="{ record }">
          <div v-if="['studentsCount', 'sendTimesCount'].includes(currentIndexCard.key)">
            <studentDetailButton :raw="{ id: record?.studentId, name: record?.name }" :eager="true" :more-info="true" />
          </div>
          <div v-else-if="currentIndexCard.key === 'teachersCount'">
            <avatarDisplay :user-info="{ id: record?.teacherId, name: record?.name }" mode="capsule" />
          </div>
          <span v-else>{{ record?.name }}</span>
        </template>

        <template #operation="{ record }">
          <span class="cursor-pointer text-blue-500" @click="handleView(record, 'operation')">查看</span>
        </template>
        <template #sendRecord="{ record }">
          <div class="flex justify-center">
            <a-popover>
              <template #content>
                <div v-for="item in record?.planInfo?.assess" :key="item?.id" class="flex flex-col">
                  <div
                    class="flex justify-between gap-3 cursor-pointer hover:text-blue-500"
                    @click="handleViewAssess(item)"
                  >
                    <span>{{ item?.udf1 }}</span>
                    <a-tag size="mini" color="green">{{ '第' + item?.numUdf1 + '次' }}</a-tag>
                  </div>
                </div>
              </template>
              <a-button v-if="record?.planInfo?.assess?.length" type="text" size="mini">评估</a-button>
            </a-popover>
            <a-button
              v-if="record?.planInfo?.iep?.length"
              type="text"
              size="mini"
              @click="handleShowPlanModal('iep', record?.planInfo?.iep)"
              >IEP
            </a-button>
            <a-button
              v-if="record?.planInfo?.send?.length"
              type="text"
              size="mini"
              @click="handleShowPlanModal('send', record?.planInfo?.send)"
              >送教计划
            </a-button>
            <span class="cursor-pointer text-blue-500" @click="handleView(record, 'sendRecord')">查看记录</span>
          </div>
        </template>
        <template #attachmentsList="{ record }">
          <span v-if="record.attachmentsList.length" class="ml-2">{{ record.attachmentsList.length }}张</span>
          <span v-else class="ml-2">0张</span>
        </template>
      </a-table>
      <div v-else class="max-w-screen-xl min-w-min m-auto">
        <photo-list :search-data="filterSearchData" type="customize" :is-simple="true" @view-send-record="handleView" />
      </div>
    </a-card>
  </div>
  <view-modal v-model="viewModalVisible" :visible="viewModalVisible" :data="viewList" :columns="columns" />
  <a-drawer
    v-if="drawerVisible"
    :visible="drawerVisible"
    width="100%"
    title="送教详情"
    @cancel="drawerVisible = false"
    @ok="drawerVisible = false"
  >
    <SendRecordViewComponent :record="currentRecord" />
  </a-drawer>
  <d2d-share-modal
    v-if="shareModalVisible"
    v-model="shareModalVisible"
    :share-all="shareAll"
    :visible="shareModalVisible"
    :filters="searchParams"
    version-type="simple"
  />
  <teachingPlanModal v-model:visible="teachingPlanVisible" :type="teachingPlanType" :plan-id="planId" />

  <analysis v-model:visible="analysisVisible" :result-id="currentAssess?.id" :current-times="currentAssess?.numUdf1" />
</template>

<style lang="less" scoped>
  .frame {
    width: 100%;
    height: calc(100vh - 50px);
    border: none;
  }

  .custom-card {
    border-radius: 0.375rem;
    margin-top: 0.75rem;
  }

  .custom-flex {
    display: flex;
    justify-content: space-around;
  }

  .custom-info-card {
    display: inline-block;
    width: 15rem;
    height: 7rem;
    background-color: #f9fafb;
    border-radius: 0.75rem;
    padding-top: 1rem;
    padding-left: 1.25rem;
    cursor: pointer;
  }

  .active-card {
    color: #2563eb;
  }

  .custom-info-value {
    margin-top: 1.25rem;
  }

  .custom-info-number {
    font-size: 1.875rem;
    color: #4a5568;
  }
</style>
