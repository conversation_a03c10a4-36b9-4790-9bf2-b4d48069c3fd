// 导航配置
export const navigationItems = [
  { key: 'studentBasic', title: '学生基础信息', icon: '👤', color: 'bg-blue-300' },
  { key: 'disabilityAssessment', title: '残疾与鉴定信息', icon: '📄', color: 'bg-purple-300' },
  { key: 'family', title: '家庭成员信息', icon: '👨‍👩‍👧‍👦', color: 'bg-green-300' },
  { key: 'healthMedical', title: '健康与医疗信息', icon: '❤️', color: 'bg-red-300' },
  { key: 'familyEnvironment', title: '家庭环境与发展信息', icon: '🏠', color: 'bg-orange-300' },
  { key: 'reinforcers', title: '强化物与偏好信息', icon: '⭐', color: 'bg-pink-300' },
  { key: 'attachments', title: '附件信息', icon: '🗂️', color: 'bg-yellow-300' },
];

// 选项配置
export const options = {
  gender: ['男', '女'],
  disabilityCategory: ['视力残疾', '听力残疾', '言语残疾', '肢体残疾', '智力残疾', '精神残疾', '多重残疾'],
  obstacleType: ['孤独症', '抽动症', '亚斯伯格症', '注意力缺陷多动症', '学习障碍', '情绪行为障碍', '学习困难', '其他'],
  disabilityLevel: ['一级', '二级', '三级', '四级'],
  education: ['文盲或半文盲', '小学', '初中', '高中/中专', '大专及以上'],
  maritalStatus: ['已婚', '离异', '丧偶', '未婚'],
  economicStatus: ['困难', '一般', '较好', '富裕'],
  yesNo: ['是', '否'],
  noneOrHas: ['无', '有'],
  livingConditions: [
    '与父母住一屋',
    '独立寝室',
    '起居室',
    '独立客厅',
    '阳台',
    '独立餐厅',
    '学习空间',
    '自己的活动空间',
  ],
  familyStructure: ['祖父', '祖母', '父亲', '母亲', '哥哥', '姐姐', '弟弟', '妹妹'],
  accompanyingSymptoms: ['无', '癫痫', '心脏病', '气喘', '过动', '精神疾患', '其他'],
  allergyTypes: ['无', '食物', '药物', '其它'],
  studentAttachmentTypeOptions: [
    { label: '课程类诊断报告', value: 'LessonDiagnosis' },
    { label: '生理类诊断报告', value: 'PhysiologicalDiagnosis' },
    { label: '心理类诊断报告', value: 'PsychologicalDiagnosis' },
    { label: '其他类诊断报告', value: 'OtherDiagnosis' },
    { label: '其他', value: 'Other' },
  ],
};

// 强化物配置
export const reinforcersConfig = [
  {
    label: '学科偏好',
    options: ['无', '数学', '语文', '唱游律动', '运动保健', '绘画手工', '生活适应', '其他'],
    key: 'subjectPreference',
  },
  {
    label: '阅读偏好',
    key: 'readingPreference',
    options: ['无', '绘本', '图画书', '故事书', '地图', '漫画书', '立体书', '贴纸书', '杂志', '其他'],
  },
  {
    label: '代币形式',
    label: '阅读偏好',
    key: 'tokenForm',
    options: [
      '无',
      '贴画 (星星或笑脸)',
      '磁铁标志',
      '小证书',
      '光荣榜',
      '购物票',
      '钱币',
      '游戏币',
      '食物',
      '徽章',
      '其他',
    ],
  },
  {
    label: '饮食偏好',
    key: 'foodPreference',
    options: [
      '无',
      '巧克力豆',
      '彩虹糖',
      '棒棒糖',
      '软糖',
      '薯片',
      '饼干',
      '小蛋糕',
      '布丁',
      '葡萄干',
      '山楂片',
      '酸奶',
      '干果',
      '果冻',
      '果汁',
      '牛奶',
      '其他',
    ],
  },
  {
    label: '物品或玩具偏好',
    key: 'toyPreference',
    options: [
      '无',
      '气球',
      '乐高',
      '积木',
      '玩偶',
      '珠宝',
      '贴画',
      '弹珠',
      '小瓶子',
      '动物模型',
      '玩具汽车或火车',
      '拼装玩具',
      '球类玩具',
      '粉笔',
      '蜡笔',
      '旋转的玩具',
      '闪闪发光的玩具',
      '昆虫模型',
      '其他',
    ],
  },
];

// 活动偏好选项（较长，单独配置）
export const activityPreferences = [
  '无',
  '当值日生或组长',
  '打扫教室卫生',
  '去超市购物',
  '当图书管理员',
  '躺在瑜伽垫上',
  '坐在跳跳球上',
  '静坐 (或遥望窗外)',
  '挠痒痒或揉肩',
  '听乐器演奏',
  '玩橡皮泥',
  '室内步行',
  '室外步行',
  '和老师击掌',
  '倒垃圾',
  '玩沙子',
  '受挤压',
  '听故事',
  '看书',
  '老师的称赞',
  '擦黑板',
  '做手工',
  '奔跑',
  '旋转',
  '蹦跳',
  '唱歌',
  '烹饪',
  '跳舞',
  '躺下',
  '涂色',
  '绘画',
  '去其他班级走动',
  '其他',
];

export const attachmentColumns = [
  {
    dataIndex: 'type',
    title: '类型',
    align: 'center',
    slotName: 'type',
  },
  {
    dataIndex: 'attachments',
    title: '附件',
    align: 'center',
    slotName: 'attachments',
  },
  { title: '操作', slotName: 'operate', width: 180, align: 'center' },
];
export const healthMedicalModel = [
  { label: '个人重大疾病/意外', options: options.noneOrHas, type: 'radio', columns: 2, key: 'hasMajorIllness' },
  { label: '伴随症状', options: options.noneOrHas, type: 'radio', columns: 3, key: 'accompanyingSymptoms' },
  { label: '定期看诊', options: options.noneOrHas, type: 'radio', columns: 2, key: 'hasRegularVisits' },
  { label: '长期用药', options: options.noneOrHas, type: 'radio', columns: 2, key: 'hasLongTermMedication' },
  { label: '过敏情况', options: options.allergyTypes, type: 'radio', columns: 2, key: 'hasAllergies' },
  { label: '医嘱', options: options.noneOrHas, type: 'radio', columns: 2, key: 'medicalAdvice' },
];

export const developHistoryModel = [
  {
    label: '生长史',
    placeholder: '请描述学生的生长发育情况',
    key: 'growthHistory',
    minRows: 4,
  },
  {
    label: '康复训练史与教育史',
    placeholder: '请描述康复训练和教育经历',
    key: 'rehabilitationHistory',
    minRows: 4,
  },
  {
    label: '未来发展期望',
    placeholder: '请描述对学生未来发展的期望',
    key: 'futureExpectations',
    minRows: 3,
  },
  {
    label: '近期发展目标期望',
    placeholder: '请描述近期发展目标',
    key: 'recentGoals',
    minRows: 3,
  },
];

export const timeSchedules = [
  {
    title: '放学后时间安排',
    icon: '📅',
    color: 'indigo',
    modelPrefix: 'afterSchoolSchedule',
  },
  {
    title: '假日时间安排',
    icon: '🏖️',
    color: 'emerald',
    modelPrefix: 'holidaySchedule',
  },
];

export const defaultBaseInfo = {
  mainDisabilityCategory: '',
  vision: '',
  IQ: null,
  leftEar: null,
  rightEar: null,
  multiDisabilitiesDescription: '',
  disabilityCategoryDescription: '',
  assessmentDate: '',
  accompanyingDisabilities: '',
  physicalExamStatus: '',
  hasIndependentSpace: null,
  activityPreference: '',
  economicStatus: '',
  tvPrograms: '',
  musicType: '',
  electronicGames: '',
  aversiveStimuli: '',
  hasMajorIllness: '',
  livingConditions: '',
  familyStructure: '',
  accompanyingSymptoms: '',
  hasRegularVisits: '',
  hasLongTermMedication: '',
  hasAllergies: '',
  medicalAdvice: '',
  parentMaritalStatus: null,
  guardianAddress: null,
  afterSchoolSchedule: { homework: 0, chores: 0, leisure: 0, other: 0, description: '' },
  holidaySchedule: { homework: 0, chores: 0, leisure: 0, other: 0, description: '' },
  fatherInfo: {
    education: '',
    occupation: '',
    age: '',
    contact: '',
  },
  motherInfo: {
    education: '',
    occupation: '',
    age: '',
    contact: '',
  },
  attachments: [],
};
