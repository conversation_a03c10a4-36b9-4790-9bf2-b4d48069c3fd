<script setup lang="ts">
  import { computed, nextTick, ref } from 'vue';
  import { IconDown, IconRight, IconPrinter } from '@arco-design/web-vue/es/icon';
  import { marked } from 'marked';
  import AttachmentPreviewModal from '@repo/ui/components/data-display/attachmentPreviewModal.vue';
  import { VuePrintNext } from 'vue-print-next';
  import UploaderModal from '@repo/ui/components/upload/uploaderModal.vue';
  import { options } from '../formConfig';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    student: {
      type: Object,
      required: true,
    },
  });

  const emits = defineEmits(['update:visible']);

  const visible = computed({
    get: () => props.visible,
    set: (val) => emits('update:visible', val),
  });

  const sameClass =
    'flex items-center justify-between p-4 bg-white border border-gray-200 rounded cursor-pointer hover:bg-gray-50 transition-colors shadow-sm';

  const expandedSections = ref({
    basic: true,
    rehabilitation: false,
    health: false,
    family: false,
    environment: false,
    reinforcers: false,
    development: false,
    attachments: false,
  });

  // 切换板块展开状态
  const toggleSection = (section: string) => {
    expandedSections.value[section] = !expandedSections.value[section];
  };

  // 格式化日期显示
  const formatDate = (date: string) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  const studentFields = [
    { key: 'name', label: '姓名' },
    { key: 'gender', label: '性别' },
    { key: 'nation', label: '民族' },
    { key: 'birthday', label: '出生日期' },
    { key: 'idCardNo', label: '身份证号' },
    { key: 'fusionSchool.name', label: '所属学校' },
    { key: 'gradeClass.name', label: '年级班级' },
    { key: 'enrollmentYear', label: '入学年份' },
    { key: 'disabilityCertificateNo', label: '残疾证号' },
    { key: 'hasSendPlan', label: '送教学生', formatter: (val) => (val ? '是' : '否') },
    { key: 'homeDeliveryInstitution.name', label: '送教机构', fullWidth: true },
    {
      key: 'rehabilitationInstitutionList',
      label: '康复机构',
      fullWidth: true,
      formatter: (list) => list?.map((item) => item.name).join('、') || '无',
    },
  ];
  const getFieldValue = (field) => {
    if (field.key.includes('.')) {
      const [parentKey, childKey] = field.key.split('.');
      return props.student?.[parentKey]?.[childKey];
    }
    return props.student?.[field.key];
  };
  const filteredStudentFields = computed(() =>
    studentFields.filter((field) => {
      const value = getFieldValue(field);
      return value !== undefined && value !== null && value !== '' && !(Array.isArray(value) && value.length === 0);
    }),
  );

  // 检查板块是否有数据
  const hasBasicInfo = computed(() => {
    const s = props.student;
    return (
      s?.name ||
      s?.gender ||
      s?.nation ||
      s?.birthday ||
      s?.fusionSchool?.name ||
      s?.gradeClass?.name ||
      s?.enrollmentYear ||
      s?.disabilityCertificateNo ||
      s?.idCardNo ||
      s?.symbol
    );
  });

  const hasRehabilitationInfo = computed(() => {
    const s = props.student;
    return (
      s?.disorders ||
      s?.disabilityLevel ||
      s?.additionalData?.baseInfo?.accompanyingDisabilities ||
      s?.additionalData?.baseInfo?.assessmentDate ||
      s?.additionalData?.assessmentInstitution ||
      s?.additionalData?.causeOfDisability ||
      s?.disabilityCertificateNo ||
      s?.additionalData?.baseInfo?.mainDisabilityCategory ||
      s?.additionalData?.disabilityPhoto?.url
    );
  });

  const hasFamilyInfo = computed(() => {
    const s = props.student;
    return s?.guardian || s?.guardianPhone || s?.address || s?.familyMembers?.length;
  });

  const hasDetailedInfo = computed(() => {
    const s = props.student;
    const baseInfo = s?.additionalData?.baseInfo;
    return (
      s?.familyStatus ||
      s?.historyOfDevelopment ||
      s?.historyOfEducation ||
      baseInfo?.growthHistory ||
      baseInfo?.rehabilitationHistory ||
      baseInfo?.futureExpectations ||
      baseInfo?.recentGoals
    );
  });

  const hasHealthMedicalInfo = computed(() => {
    const baseInfo = props.student?.additionalData?.baseInfo;
    return (
      baseInfo?.hasMajorIllness ||
      baseInfo?.accompanyingSymptoms ||
      baseInfo?.hasRegularVisits ||
      baseInfo?.hasLongTermMedication ||
      baseInfo?.hasAllergies ||
      baseInfo?.medicalAdvice
    );
  });
  const timeScheduleVisible = (item: any) => {
    return item?.homework || item?.chores || item?.leisure || item?.other;
  };
  const hasFamilyEnvironmentInfo = computed(() => {
    const baseInfo = props.student?.additionalData?.baseInfo;
    return (
      props.student?.additionalData?.onlyChild !== undefined ||
      baseInfo?.hasIndependentSpace !== undefined ||
      timeScheduleVisible(baseInfo?.afterSchoolSchedule) ||
      timeScheduleVisible(baseInfo?.holidaySchedule) ||
      baseInfo?.parentMaritalStatus
    );
  });

  const hasReinforcersInfo = computed(() => {
    const baseInfo = props.student?.additionalData?.baseInfo;
    return (
      baseInfo?.subjectPreference?.length ||
      baseInfo?.readingPreference?.length ||
      baseInfo?.tokenForm?.length ||
      baseInfo?.foodPreference?.length ||
      baseInfo?.toyPreference?.length ||
      baseInfo?.activityPreference?.length ||
      baseInfo?.aversiveStimuli
    );
  });

  const hasAttachments = computed(() => {
    return props.student?.attachments?.length > 0;
  });

  // 格式化数组显示
  const formatArray = (arr: string[] | undefined) => {
    if (!arr || !Array.isArray(arr) || arr.length === 0) return '无';
    return arr.filter((item) => item && item !== '无').join('、') || '无';
  };

  const baseInfo = computed(() => {
    return props.student?.additionalData?.baseInfo || {};
  });

  const getTypeName = (type: string) => {
    return options.studentAttachmentTypeOptions.find((t) => t.value === type)?.label || '附件';
  };
  const currentAttachment = ref([]);
  const view = ref(false);
  const handleViewAttachment = (item: any) => {
    currentAttachment.value = item;
    view.value = true;
  };

  const getDisableInfo = (disorders: string) => {
    switch (disorders) {
      case '视力残疾':
        return { label: '视力障碍类型：', value: props.student?.additionalData?.baseInfo?.vision };
      case '智力残疾':
        return { label: 'IQ值：', value: props.student?.additionalData?.baseInfo?.IQ };
      case '多重残疾':
        return {
          label: '多重障碍说明：',
          value: props.student?.additionalData?.baseInfo?.multiDisabilitiesDescription,
        };
      default:
        return {};
    }
  };

  const handlePrint = async () => {
    expandedSections.value = Object.fromEntries(Object.keys(expandedSections.value).map((key) => [key, true]));
    await nextTick();
    // eslint-disable-next-line no-new
    new VuePrintNext({
      el: `#studentDetail`,
      popTitle: `${props.student.name} - 学生详细信息`,
      zIndex: 9999,
      printMode: 'popup',
      hide: '.no-print',
    });
  };
</script>

<template>
  <a-modal v-model:visible="visible" width="75%" :footer="false" class="student-detail-modal" :render-to-body="false">
    <template #title>
      <div class="flex justify-between items-center w-full">
        <span class="flex-grow text-center font-semibold text-lg">{{ student?.name }} - 学生详细信息</span>
        <a-button size="mini" class="flex-shrink-0 no-print mr-6" @click="handlePrint">
          <template #icon>
            <IconPrinter />
          </template>
          打印
        </a-button>
      </div>
    </template>

    <!-- 主要内容区域 -->
    <div class="flex h-[75vh] relative">
      <!-- 右侧内容区域 -->
      <div id="studentDetail" class="flex-1 overflow-y-auto">
        <div class="print-content p-6 lg:p-8">
          <!-- 学生基本信息头部 -->
          <div
            class="flex flex-col sm:flex-row sm:items-center gap-4 p-6 rounded mb-8 bg-gradient-to-r from-white"
            :class="
              student?.gender === '男'
                ? 'to-blue-50 border-l-4 border-blue-400'
                : 'to-pink-50 border-l-4 border-pink-400'
            "
          >
            <div class="w-20 h-24 rounded flex items-center justify-center shadow-sm flex-shrink-0">
              <img
                v-if="student?.additionalData?.avatar?.url"
                :src="student?.additionalData?.avatar?.url"
                class="w-full h-full object-cover rounded"
                alt="学生头像"
              />
              <span v-else class="text-xs">暂无照片</span>
            </div>

            <div class="flex-1 min-w-0">
              <h2 class="text-2xl font-bold text-gray-900 mb-3">{{ student?.name }}</h2>
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                <div v-if="student?.gender" class="flex items-center">
                  <span class="text-gray-500 w-16">性别：</span>
                  <span class="text-gray-900 font-medium">{{ student.gender }}</span>
                </div>
                <div v-if="student?.birthday" class="flex items-center">
                  <span class="text-gray-500 w-16">年龄：</span>
                  <span class="text-gray-900 font-medium">
                    {{ student?.age }}岁
                    <span class="text-gray-500 text-xs ml-1">({{ formatDate(student.birthday) }})</span>
                  </span>
                </div>
                <div v-if="student?.fusionSchool?.name" class="flex items-center">
                  <span class="text-gray-500 w-16">学校：</span>
                  <span class="text-gray-900 font-medium">{{ student.fusionSchool.name }}</span>
                </div>
                <div v-if="student?.gradeClass?.name" class="flex items-center">
                  <span class="text-gray-500 w-16">班级：</span>
                  <span class="text-gray-900 font-medium">{{ student.gradeClass.name }}</span>
                </div>
                <div v-if="student?.symbol" class="flex items-center col-span-full">
                  <span class="text-gray-500 w-16">学籍号：</span>
                  <span class="text-gray-900 font-medium">{{ student.symbol }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 基本信息板块 -->
        <div v-if="hasBasicInfo" id="section-basic" class="print-section mb-8">
          <div :class="sameClass" @click.stop="toggleSection('basic')">
            <div class="flex items-center gap-3">
              <div class="w-1 h-6 bg-blue-500 rounded-full"></div>
              <span class="font-semibold text-gray-900 text-lg print-section-title">学生基础信息</span>
            </div>
            <IconDown v-if="expandedSections.basic" class="text-gray-400 transition-transform" />
            <IconRight v-else class="text-gray-400 transition-transform" />
          </div>

          <div
            v-if="expandedSections.basic"
            class="mt-4 p-6 bg-gradient-to-r from-blue-50 to-white rounded border border-blue-100"
          >
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div v-for="field in filteredStudentFields" :key="field.key" class="flex items-center">
                <span class="w-24 text-gray-600 font-medium">{{ field.label }}：</span>
                <span class="text-gray-900">
                  {{ field.formatter ? field.formatter(getFieldValue(field)) : getFieldValue(field) }}
                </span>
              </div>
              <div v-if="student?.additionalData?.householdPhoto?.url" class="flex items-center">
                <span class="w-24 text-gray-600 font-medium">户口本照片：</span>
                <span class="text-gray-900">
                  <a-button size="mini" @click="handleViewAttachment([student.additionalData.householdPhoto])"
                    >查看附件</a-button
                  >
                </span>
              </div>
            </div>
          </div>
        </div>
        <!-- 康复信息板块 -->
        <div v-if="hasRehabilitationInfo" id="section-rehabilitation" class="print-section mb-8">
          <div :class="sameClass" @click.stop="toggleSection('rehabilitation')">
            <div class="flex items-center gap-3">
              <div class="w-1 h-6 bg-red-500 rounded-full"></div>
              <span class="font-semibold text-gray-900 text-lg print-section-title">残疾与鉴定信息</span>
            </div>
            <IconDown v-if="expandedSections.rehabilitation" class="text-gray-400 transition-transform" />
            <IconRight v-else class="text-gray-400 transition-transform" />
          </div>

          <div
            v-if="expandedSections.rehabilitation"
            class="mt-4 p-6 bg-gradient-to-r from-red-50 to-white rounded border border-red-100 space-y-6"
          >
            <!-- Section: 残疾基本信息 -->
            <div class="bg-white p-6 rounded border border-gray-200 shadow-sm">
              <h5 class="font-semibold text-gray-900 mb-4 text-lg border-b border-gray-200 pb-2">残疾基本信息</h5>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div v-if="student?.additionalData?.baseInfo?.mainDisabilityCategory" class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">主残疾类别：</span>
                  <span class="text-gray-900">{{ student.additionalData.baseInfo.mainDisabilityCategory }}</span>
                </div>
                <div v-if="student?.disabilityLevel" class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">残疾程度：</span>
                  <span class="text-gray-900">{{ student.disabilityLevel }}</span>
                </div>
                <div
                  v-if="student?.additionalData?.baseInfo?.accompanyingDisabilities?.length"
                  class="flex items-center"
                >
                  <span class="w-32 text-gray-600 font-medium">伴随残疾：</span>
                  <span class="text-gray-900">{{
                    formatArray(student.additionalData.baseInfo.accompanyingDisabilities)
                  }}</span>
                </div>
                <div v-if="student?.disorders" class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">障碍类型：</span>
                  <span class="text-gray-900">
                    {{ Array.isArray(student.disorders) ? student.disorders.join('、') : student.disorders }}
                  </span>
                </div>
                <div
                  v-if="getDisableInfo(student.disorders)?.label && getDisableInfo(student.disorders)?.value"
                  class="flex items-center col-span-full"
                >
                  <span class="w-32 text-gray-600 font-medium">{{ getDisableInfo(student.disorders)?.label }}</span>
                  <span class="text-gray-900">{{ getDisableInfo(student.disorders)?.value }}</span>
                </div>
                <div v-if="student?.additionalData?.disabilityPhoto?.url" class="flex items-center">
                  <span class="w-24 text-gray-600 font-medium">残疾证照片：</span>
                  <span class="text-gray-900">
                    <a-button size="mini" @click="handleViewAttachment([student.additionalData.disabilityPhoto])"
                      >查看附件</a-button
                    >
                  </span>
                </div>
              </div>
            </div>

            <!-- Section: 鉴定信息 -->
            <div class="bg-white p-6 rounded border border-gray-200 shadow-sm">
              <h5 class="font-semibold text-gray-900 mb-4 text-lg border-b border-gray-200 pb-2">评估与鉴定</h5>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div v-if="student?.additionalData?.baseInfo?.assessmentDate" class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">评估日期：</span>
                  <span class="text-gray-900">{{ formatDate(student.additionalData.baseInfo.assessmentDate) }}</span>
                </div>
                <div v-if="student?.additionalData?.assessmentInstitution" class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">评估机构：</span>
                  <span class="text-gray-900">{{ student.additionalData.assessmentInstitution }}</span>
                </div>
                <div v-if="student?.disabilityCertificateNo" class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">残疾证号：</span>
                  <span class="text-gray-900">{{ student.disabilityCertificateNo }}</span>
                </div>
                <div v-if="student?.additionalData?.causeOfDisability" class="flex items-center col-span-full">
                  <span class="w-32 text-gray-600 font-medium">致残原因：</span>
                  <span class="text-gray-900">{{ student.additionalData.causeOfDisability }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 健康与医疗信息板块 -->
        <div v-if="hasHealthMedicalInfo" id="section-health" class="print-section mb-8">
          <div :class="sameClass" @click.stop="toggleSection('health')">
            <div class="flex items-center gap-3">
              <div class="w-1 h-6 bg-green-500 rounded-full"></div>
              <span class="font-semibold text-gray-900 text-lg print-section-title">健康与医疗信息</span>
            </div>
            <IconDown v-if="expandedSections.health" class="text-gray-400 transition-transform" />
            <IconRight v-else class="text-gray-400 transition-transform" />
          </div>

          <div
            v-if="expandedSections.health"
            class="mt-4 p-6 bg-gradient-to-r from-green-50 to-white rounded border border-green-100"
          >
            <div class="bg-white p-6 rounded border border-gray-200 shadow-sm">
              <h5 class="font-semibold text-gray-900 mb-4 text-lg border-b border-gray-200 pb-2">身体检查情况</h5>
              <p
                class="text-gray-700 text-sm leading-relaxed mb-4"
                v-html="marked(student?.additionalData?.baseInfo?.physicalExamStatus)"
              />

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">重大疾病/意外：</span>
                  <span class="text-gray-900">{{ student?.additionalData?.baseInfo?.hasMajorIllness || '无' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">伴随症状：</span>
                  <span class="text-gray-900">{{
                    student?.additionalData?.baseInfo?.accompanyingSymptoms || '无'
                  }}</span>
                </div>
                <div class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">定期看诊：</span>
                  <span class="text-gray-900">{{ student?.additionalData?.baseInfo?.hasRegularVisits || '无' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">长期用药：</span>
                  <span class="text-gray-900">{{
                    student?.additionalData?.baseInfo?.hasLongTermMedication || '无'
                  }}</span>
                </div>
                <div class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">过敏情况：</span>
                  <span class="text-gray-900">{{ student?.additionalData?.baseInfo?.hasAllergies || '无' }}</span>
                </div>
                <div class="flex items-center">
                  <span class="w-32 text-gray-600 font-medium">医嘱：</span>
                  <span class="text-gray-900">{{ student?.additionalData?.baseInfo?.medicalAdvice || '无' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 家庭状况板块 -->
        <div v-if="hasFamilyInfo" id="section-family" class="print-section mb-8">
          <div :class="sameClass" @click.stop="toggleSection('family')">
            <div class="flex items-center gap-3">
              <div class="w-1 h-6 bg-amber-500 rounded-full"></div>
              <span class="font-semibold text-gray-900 text-lg print-section-title">家庭成员信息</span>
            </div>
            <IconDown v-if="expandedSections.family" class="text-gray-400 transition-transform" />
            <IconRight v-else class="text-gray-400 transition-transform" />
          </div>

          <div
            v-if="expandedSections.family"
            class="mt-4 p-6 bg-gradient-to-r from-amber-50 to-white rounded border border-amber-100"
          >
            <!-- 监护人信息 -->
            <div v-if="student?.guardian || student?.guardianPhone" class="mb-4 p-3 bg-white rounded">
              <h4 class="font-medium text-gray-900 mb-3 flex items-center">
                <span class="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center text-white text-xs mr-2"
                  >👤</span
                >
                监护人信息
              </h4>
              <div class="grid grid-cols-2 gap-3">
                <div v-if="student?.guardian" class="flex">
                  <span class="w-16 text-gray-600">姓名：</span>
                  <span class="text-gray-900">{{ student.guardian }}</span>
                </div>
                <div v-if="student?.guardianPhone" class="flex">
                  <span class="w-16 text-gray-600">联系电话：</span>
                  <span class="text-gray-900">{{ student.guardianPhone }}</span>
                </div>
              </div>
            </div>

            <!-- 家庭成员 -->
            <div v-for="item in ['fatherInfo', 'motherInfo']" :key="item">
              <div v-if="baseInfo?.[item]?.education || student?.familyMembers?.length" class="mb-4">
                <h4 class="font-medium text-gray-900 mb-3">家庭成员</h4>
                <div class="space-y-3">
                  <div
                    v-if="
                      baseInfo?.[item]?.education ||
                      baseInfo?.[item]?.occupation ||
                      baseInfo?.[item]?.age ||
                      baseInfo?.[item]?.contact
                    "
                    class="p-3 border rounded"
                    :class="item === 'motherInfo' ? 'bg-pink-50 border-pink-200' : 'bg-blue-50 border-blue-200'"
                  >
                    <h5 class="font-medium text-pink-900 mb-2 flex items-center">
                      <span
                        :class="item === 'motherInfo' ? 'bg-pink-500' : 'bg-blue-500'"
                        class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs mr-2"
                        >👩</span
                      >
                      {{ item === 'motherInfo' ? '母亲' : '父亲' }}信息
                    </h5>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                      <div v-if="baseInfo?.[item]?.education" class="flex">
                        <span class="w-16 text-gray-600">教育程度：</span>
                        <span class="text-gray-900">{{ baseInfo?.[item]?.education }}</span>
                      </div>
                      <div v-if="baseInfo?.[item]?.occupation" class="flex">
                        <span class="w-16 text-gray-600">职业：</span>
                        <span class="text-gray-900">{{ baseInfo?.[item]?.occupation }}</span>
                      </div>
                      <div v-if="baseInfo?.[item]?.age" class="flex">
                        <span class="w-16 text-gray-600">年龄：</span>
                        <span class="text-gray-900">{{ baseInfo?.[item]?.age }}</span>
                      </div>
                      <div v-if="baseInfo?.[item]?.contact" class="flex">
                        <span class="w-16 text-gray-600">联系电话：</span>
                        <span class="text-gray-900">{{ baseInfo?.[item]?.contact }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="student?.address" class="flex">
              <span class="w-20 text-gray-600">联系地址：</span>
              <span class="text-gray-900">{{ student.address }}</span>
            </div>
          </div>
        </div>

        <!-- 家庭环境与发展信息板块 -->
        <div v-if="hasFamilyEnvironmentInfo" id="section-environment" class="print-section mb-8">
          <div :class="sameClass" @click.stop="toggleSection('environment')">
            <div class="flex items-center gap-3">
              <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
              <span class="font-semibold text-gray-900 text-lg print-section-title">家庭环境与发展信息</span>
            </div>
            <IconDown v-if="expandedSections.environment" class="text-gray-400 transition-transform" />
            <IconRight v-else class="text-gray-400 transition-transform" />
          </div>

          <div
            v-if="expandedSections.environment"
            class="mt-4 p-6 bg-gradient-to-r from-orange-50 to-white rounded border border-orange-100"
          >
            <div class="space-y-4">
              <!-- 基本家庭信息 -->
              <div class="grid grid-cols-2 gap-4">
                <div v-if="student?.additionalData?.onlyChild !== undefined" class="flex">
                  <span class="w-24 text-gray-600">是否独生子女：</span>
                  <span class="text-gray-900">{{ student?.additionalData.onlyChild }}</span>
                </div>
                <div v-if="student?.additionalData?.baseInfo?.hasIndependentSpace !== undefined" class="flex">
                  <span class="w-24 text-gray-600">是否有独立生活/学习空间：</span>
                  <span class="text-gray-900">
                    {{ student?.additionalData.baseInfo.hasIndependentSpace ? '是' : '否' }}
                  </span>
                </div>
              </div>

              <!-- 时间安排 -->
              <div
                v-if="timeScheduleVisible(student?.additionalData?.baseInfo?.afterSchoolSchedule)"
                class="bg-indigo-50 border border-indigo-200 rounded p-4"
              >
                <h5 class="font-medium text-indigo-900 mb-3 flex items-center">
                  <span
                    class="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center text-white text-xs mr-2"
                    >📅</span
                  >
                  放学后时间安排
                </h5>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                  <div class="flex">
                    <span class="w-16 text-gray-600">做功课：</span>
                    <span class="text-gray-900"
                      >{{ student?.additionalData.baseInfo.afterSchoolSchedule.homework || 0 }}小时</span
                    >
                  </div>
                  <div class="flex">
                    <span class="w-16 text-gray-600">帮忙家务：</span>
                    <span class="text-gray-900"
                      >{{ student?.additionalData.baseInfo.afterSchoolSchedule.chores || 0 }}小时</span
                    >
                  </div>
                  <div class="flex">
                    <span class="w-16 text-gray-600">休闲：</span>
                    <span class="text-gray-900"
                      >{{ student?.additionalData.baseInfo.afterSchoolSchedule.leisure || 0 }}小时</span
                    >
                  </div>
                  <div class="flex">
                    <span class="w-16 text-gray-600">其他：</span>
                    <span class="text-gray-900"
                      >{{ student?.additionalData.baseInfo.afterSchoolSchedule.other || 0 }}小时</span
                    >
                  </div>
                </div>
                <div v-if="student?.additionalData.baseInfo.afterSchoolSchedule.description" class="mt-3">
                  <span class="text-gray-600">其他活动说明：</span>
                  <span class="text-gray-900">{{
                    student?.additionalData.baseInfo.afterSchoolSchedule.description
                  }}</span>
                </div>
              </div>

              <div
                v-if="timeScheduleVisible(student?.additionalData?.baseInfo?.holidaySchedule)"
                class="bg-emerald-50 border border-emerald-200 rounded p-4"
              >
                <h5 class="font-medium text-emerald-900 mb-3 flex items-center">
                  <span
                    class="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center text-white text-xs mr-2"
                    >🏖️</span
                  >
                  假日时间安排
                </h5>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                  <div class="flex">
                    <span class="w-16 text-gray-600">做功课：</span>
                    <span class="text-gray-900"
                      >{{ student?.additionalData.baseInfo.holidaySchedule.homework || 0 }}小时</span
                    >
                  </div>
                  <div class="flex">
                    <span class="w-16 text-gray-600">帮忙家务：</span>
                    <span class="text-gray-900"
                      >{{ student?.additionalData.baseInfo.holidaySchedule.chores || 0 }}小时</span
                    >
                  </div>
                  <div class="flex">
                    <span class="w-16 text-gray-600">休闲：</span>
                    <span class="text-gray-900"
                      >{{ student?.additionalData.baseInfo.holidaySchedule.leisure || 0 }}小时</span
                    >
                  </div>
                  <div class="flex">
                    <span class="w-16 text-gray-600">其他：</span>
                    <span class="text-gray-900"
                      >{{ student?.additionalData.baseInfo.holidaySchedule.other || 0 }}小时</span
                    >
                  </div>
                </div>
                <div v-if="student?.additionalData.baseInfo.holidaySchedule.description" class="mt-3">
                  <span class="text-gray-600">其他活动说明：</span>
                  <span class="text-gray-900">{{ student?.additionalData.baseInfo.holidaySchedule.description }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 强化物与偏好信息板块 -->
        <div v-if="hasReinforcersInfo" id="section-reinforcers" class="print-section mb-8">
          <div :class="sameClass" @click.stop="toggleSection('reinforcers')">
            <div class="flex items-center gap-3">
              <div class="w-1 h-6 bg-pink-500 rounded-full"></div>
              <span class="font-semibold text-gray-900 text-lg print-section-title">强化物与偏好信息</span>
            </div>
            <IconDown v-if="expandedSections.reinforcers" class="text-gray-400 transition-transform" />
            <IconRight v-else class="text-gray-400 transition-transform" />
          </div>

          <div
            v-if="expandedSections.reinforcers"
            class="mt-4 p-6 bg-gradient-to-r from-pink-50 to-white rounded border border-pink-100"
          >
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div v-if="student?.additionalData?.baseInfo?.subjectPreference?.length" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">学科偏好</h5>
                <p class="text-gray-700 text-sm">{{
                  formatArray(student?.additionalData.baseInfo.subjectPreference)
                }}</p>
              </div>
              <div v-if="student?.additionalData?.baseInfo?.readingPreference?.length" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">阅读偏好</h5>
                <p class="text-gray-700 text-sm">{{
                  formatArray(student?.additionalData.baseInfo.readingPreference)
                }}</p>
              </div>
              <div v-if="student?.additionalData?.baseInfo?.tokenForm?.length" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">代币形式</h5>
                <p class="text-gray-700 text-sm">{{ formatArray(student?.additionalData.baseInfo.tokenForm) }}</p>
              </div>
              <div v-if="student?.additionalData?.baseInfo?.foodPreference?.length" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">饮食偏好</h5>
                <p class="text-gray-700 text-sm">{{ formatArray(student?.additionalData.baseInfo.foodPreference) }}</p>
              </div>
              <div v-if="student?.additionalData?.baseInfo?.toyPreference?.length" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">物品或玩具偏好</h5>
                <p class="text-gray-700 text-sm">{{ formatArray(student?.additionalData.baseInfo.toyPreference) }}</p>
              </div>
            </div>

            <div v-if="student?.additionalData?.baseInfo?.activityPreference?.length" class="mt-4 bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">活动或游戏偏好</h5>
              <p class="text-gray-700 text-sm">{{
                formatArray(student?.additionalData.baseInfo.activityPreference)
              }}</p>
            </div>

            <div v-if="student?.additionalData?.baseInfo?.aversiveStimuli" class="mt-4 bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">厌恶刺激</h5>
              <p
                class="text-gray-700 text-sm"
                v-html="marked(student?.additionalData.baseInfo.aversiveStimuli || '')"
              />
            </div>
          </div>
        </div>

        <!-- 发展历史信息板块 -->
        <div v-if="hasDetailedInfo" id="section-development" class="print-section mb-8">
          <div :class="sameClass" @click.stop="toggleSection('development')">
            <div class="flex items-center gap-3">
              <div class="w-1 h-6 bg-purple-500 rounded-full"></div>
              <span class="font-semibold text-gray-900 text-lg print-section-title">发展历史信息</span>
            </div>
            <IconDown v-if="expandedSections.development" class="text-gray-400 transition-transform" />
            <IconRight v-else class="text-gray-400 transition-transform" />
          </div>

          <div
            v-if="expandedSections.development"
            class="mt-4 p-6 bg-gradient-to-r from-purple-50 to-white rounded border border-purple-100"
          >
            <div class="space-y-4">
              <div v-if="student?.additionalData?.baseInfo?.growthHistory" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">生长史</h5>
                <p
                  class="text-gray-700 text-sm leading-relaxed"
                  v-html="marked(student?.additionalData.baseInfo.growthHistory || '')"
                />
              </div>
              <div v-if="student?.additionalData?.baseInfo?.rehabilitationHistory" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">康复训练史与教育史</h5>
                <p
                  class="text-gray-700 text-sm leading-relaxed"
                  v-html="marked(student?.additionalData.baseInfo.rehabilitationHistory || '')"
                />
              </div>
              <div v-if="student?.additionalData?.baseInfo?.futureExpectations" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">未来发展期望</h5>
                <p
                  class="text-gray-700 text-sm leading-relaxed"
                  v-html="marked(student?.additionalData.baseInfo.futureExpectations || '')"
                />
              </div>
              <div v-if="student?.additionalData?.baseInfo?.recentGoals" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">近期发展目标期望</h5>
                <p
                  class="text-gray-700 text-sm leading-relaxed"
                  v-html="marked(student?.additionalData.baseInfo.recentGoals || '')"
                />
              </div>
              <!-- 兼容旧数据格式 -->
              <div v-if="student?.familyStatus" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">家庭状况</h5>
                <p class="text-gray-700 text-sm leading-relaxed">{{ student.familyStatus }}</p>
              </div>
              <div v-if="student?.historyOfDevelopment" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">发展史</h5>
                <p class="text-gray-700 text-sm leading-relaxed">{{ student.historyOfDevelopment }}</p>
              </div>
              <div v-if="student?.historyOfEducation" class="bg-white p-3 rounded">
                <h5 class="font-medium text-gray-900 mb-2">教育史</h5>
                <p class="text-gray-700 text-sm leading-relaxed">{{ student.historyOfEducation }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 附件信息板块 -->
        <div v-if="hasAttachments" id="section-attachments" class="print-section mb-8">
          <div :class="sameClass" @click.stop="toggleSection('attachments')">
            <div class="flex items-center gap-3">
              <div class="w-1 h-6 bg-indigo-500 rounded-full"></div>
              <span class="font-semibold text-gray-900 text-lg print-section-title">家长反馈信息</span>
            </div>
            <IconDown v-if="expandedSections.attachments" class="text-gray-400 transition-transform" />
            <IconRight v-else class="text-gray-400 transition-transform" />
          </div>
          <div
            v-if="expandedSections.attachments"
            class="mt-4 p-6 bg-gradient-to-r from-indigo-50 to-white rounded border border-indigo-100"
          >
            <div class="space-y-3">
              <div
                v-for="(attachment, index) in student.attachments"
                :key="index"
                class="bg-white p-4 rounded border cursor-pointer hover:border-indigo-300 transition-all shadow-sm"
                @click="handleViewAttachment(attachment?.attachments)"
              >
                <div class="flex justify-between items-center">
                  <span class="font-medium text-gray-900">{{ getTypeName(attachment.type) || '附件' }}</span>
                  <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full"
                    >{{ attachment.attachments?.length || 0 }} 个文件</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <attachment-preview-modal v-model="view" :current-file-index="0" :files-list="currentAttachment" />
  </a-modal>
</template>

<style scoped></style>
