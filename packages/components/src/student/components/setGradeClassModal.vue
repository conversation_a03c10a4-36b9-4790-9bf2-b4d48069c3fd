<script setup lang="ts">
  import { computed, onMounted, PropType, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { isArray } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import { getOrgNature } from '../../utils/utils';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    sameSchool: {
      type: Boolean,
      default: false,
    },
    records: {
      type: [Array, Object] as PropType<any>,
      default: () => [],
    },
    uniqueBranchOffice: {
      type: Array,
      default: () => [],
    },
  });

  const emits = defineEmits(['update:visible', 'ok']);

  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(val: boolean) {
      emits('update:visible', val);
    },
  });

  const selectClass = ref<number>();
  const saving = ref(false);
  const gradeClassStore = useCommonStore({
    api: '/resourceRoom/gradeClass',
    queryParams: {
      orgNature: getOrgNature(),
    },
  });

  const studentName = computed(() => {
    return isArray(props.records) ? `${props.records.length}名学生` : props.records.name;
  });
  const studentIds = computed(() => {
    return isArray(props.records) ? props.records.map((record: any) => record.id || record) : [props.records.id];
  });
  const classData = ref<any[]>([]);
  const initClassData = async () => {
    classData.value = await gradeClassStore.getOptions();
  };

  const handelOk = async () => {
    saving.value = true;
    try {
      await request(`resourceRoom/student/updateClassByIds/${selectClass.value}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'post',
        data: studentIds.value,
      });

      Message.success('设置成功');
      emits('ok');
    } finally {
      saving.value = false;
    }
  };

  onMounted(async () => {
    await initClassData();
  });
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    :closable="false"
    :title="'修改' + studentName + '的班级'"
    :on-before-ok="handelOk"
    :loading="saving"
    @open="handleOpen"
  >
    <a-select
      v-model="selectClass"
      :disabled="!sameSchool"
      :placeholder="sameSchool ? '请选择班级' : '学校不同不能批量修改班级'"
      :options="
        classData.filter((c) => {
          return uniqueBranchOffice.values().next().value === c.raw.boId;
        })
      "
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
