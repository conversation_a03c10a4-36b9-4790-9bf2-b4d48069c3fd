<script lang="ts" setup>
import { ref, computed, PropType } from 'vue';
import { IconPlus, IconEdit, IconDelete, IconUser } from '@arco-design/web-vue/es/icon';
import { AnnotationModuleSource } from '../../../utils/annotationBlock';

const props = defineProps({
  chapter: {
    type: Object,
    required: true,
  },
  course: {
    type: Object,
    required: true,
  },
  annotationSource: {
    type: Object as PropType<AnnotationModuleSource>,
    required: true,
  },
  annotations: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);

// 统计数据
const stats = ref([
  { label: '活跃行为项', value: 2, icon: '⭐', color: 'rgb(255, 193, 7)' },
  { label: '参与学生', value: 4, icon: '👥', color: 'rgb(0, 123, 255)' },
  { label: '平均表现', value: '0.0', icon: '📊', color: 'rgb(40, 167, 69)' },
  { label: '实现最佳', value: '张小明', icon: '🏆', color: 'rgb(255, 193, 7)' },
]);

// 行为项目数据
const behaviorItems = ref([
  {
    id: 1,
    name: '安静坐好',
    description: '能够在座位上保持安静，不随意走动',
    status: '启用中',
    difficulty: '5分',
    students: [
      { name: '张小明', scores: [0, 1, 2, 3, 4, 5] },
      { name: '王小强', scores: [0, 1, 2, 3, 4, 5] }
    ]
  },
  {
    id: 2,
    name: '举手发言',
    description: '主动举手回答问题或表达想法',
    status: '启用中',
    difficulty: '5分',
    students: [
      { name: '李小红', scores: [0, 1, 2, 3, 4, 5] },
      { name: '刘小美', scores: [0, 1, 2, 3, 4, 5] }
    ]
  }
]);

const addBehaviorVisible = ref(false);
const newBehaviorForm = ref({
  name: '',
  description: '',
  difficulty: 5
});

const handleAddBehavior = () => {
  addBehaviorVisible.value = true;
};

const handleSaveBehavior = () => {
  const newBehavior = {
    id: Date.now(),
    name: newBehaviorForm.value.name,
    description: newBehaviorForm.value.description,
    status: '启用中',
    difficulty: `${newBehaviorForm.value.difficulty}分`,
    students: []
  };
  
  behaviorItems.value.push(newBehavior);
  addBehaviorVisible.value = false;
  
  // 重置表单
  newBehaviorForm.value = {
    name: '',
    description: '',
    difficulty: 5
  };
};

const handleEditBehavior = (item: any) => {
  console.log('编辑行为项目', item);
};

const handleDeleteBehavior = (item: any) => {
  const index = behaviorItems.value.findIndex(b => b.id === item.id);
  if (index > -1) {
    behaviorItems.value.splice(index, 1);
  }
};

const handleScoreChange = (behaviorId: number, studentName: string, scoreIndex: number) => {
  console.log('评分变更', { behaviorId, studentName, scoreIndex });
};
</script>

<template>
  <div class="teaching-design-record">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
      <div
        v-for="stat in stats"
        :key="stat.label"
        class="bg-white rounded-lg p-4 border hover:shadow-md transition-shadow"
      >
        <div class="flex items-center gap-3">
          <div 
            class="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg"
            :style="{ backgroundColor: stat.color }"
          >
            {{ stat.icon }}
          </div>
          <div>
            <div class="text-2xl font-bold text-gray-800">{{ stat.value }}</div>
            <div class="text-sm text-gray-500">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行为项目设计 -->
    <div class="bg-white rounded-lg border">
      <div class="px-6 py-4 border-b bg-gray-50 rounded-t-lg">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-800">行为项目设计</h3>
          <button
            @click="handleAddBehavior"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <IconPlus class="w-4 h-4" />
            添加行为项
          </button>
        </div>
      </div>

      <div class="p-6">
        <div v-if="behaviorItems.length === 0" class="text-center py-8 text-gray-500">
          暂无行为项目，请先添加
        </div>

        <div v-else class="space-y-6">
          <div
            v-for="item in behaviorItems"
            :key="item.id"
            class="border rounded-lg p-6"
          >
            <!-- 行为项目头部 -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <h4 class="text-lg font-medium text-gray-800">{{ item.name }}</h4>
                  <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
                    {{ item.status }}
                  </span>
                </div>
                <p class="text-gray-600 mb-2">{{ item.description }}</p>
                <div class="text-sm text-gray-500">
                  最高分值: {{ item.difficulty }}
                </div>
              </div>
              
              <div class="flex gap-2">
                <button
                  @click="handleEditBehavior(item)"
                  class="p-2 text-gray-500 hover:text-blue-600 transition-colors"
                  title="编辑"
                >
                  <IconEdit class="w-4 h-4" />
                </button>
                <button
                  @click="handleDeleteBehavior(item)"
                  class="p-2 text-gray-500 hover:text-red-600 transition-colors"
                  title="删除"
                >
                  <IconDelete class="w-4 h-4" />
                </button>
              </div>
            </div>

            <!-- 学生行为评分 -->
            <div v-if="item.students.length > 0">
              <h5 class="font-medium text-gray-700 mb-3">学生行为评分:</h5>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div
                  v-for="student in item.students"
                  :key="student.name"
                  class="flex items-center gap-4 p-3 bg-gray-50 rounded-lg"
                >
                  <div class="flex items-center gap-2">
                    <div class="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-white text-sm">
                      👤
                    </div>
                    <span class="font-medium text-gray-700">{{ student.name }}</span>
                  </div>
                  
                  <div class="flex gap-1">
                    <button
                      v-for="(score, index) in student.scores"
                      :key="index"
                      @click="handleScoreChange(item.id, student.name, index)"
                      class="w-8 h-8 rounded-full border-2 border-gray-300 hover:border-blue-500 transition-colors flex items-center justify-center text-sm font-medium"
                      :class="index === 0 ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'"
                    >
                      {{ index }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div v-else class="text-center py-4 text-gray-500 bg-gray-50 rounded-lg">
              暂无学生参与此行为项目
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加行为项目弹窗 -->
    <a-modal
      v-model:visible="addBehaviorVisible"
      title="添加行为项目"
      @ok="handleSaveBehavior"
      @cancel="addBehaviorVisible = false"
    >
      <a-form :model="newBehaviorForm" layout="vertical">
        <a-form-item label="行为项目名称" required>
          <a-input
            v-model="newBehaviorForm.name"
            placeholder="请输入行为项目名称"
          />
        </a-form-item>
        
        <a-form-item label="行为描述" required>
          <a-textarea
            v-model="newBehaviorForm.description"
            placeholder="请描述该行为的具体要求"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="最高分值">
          <a-input-number
            v-model="newBehaviorForm.difficulty"
            :min="1"
            :max="10"
            :default-value="5"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped lang="scss">
.teaching-design-record {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}
</style>
