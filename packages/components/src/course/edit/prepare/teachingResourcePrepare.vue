<script lang="ts" setup>
  import { ref, computed, PropType, onMounted, nextTick } from 'vue';
  import { IconUpload, IconDownload, IconEye, IconDelete, IconCopy, IconUser } from '@arco-design/web-vue/es/icon';
  import DigitalResourceSelect from '@repo/components/resource/digital/components/digitalResourceSelect.vue';
  import UploaderButton from '@repo/ui/components/upload/uploaderButton.vue';
  import openapi from '@repo/infrastructure/openapi';
  import { collapsedNameDisplay } from '@repo/infrastructure/ui';
  import { AnnotationModuleSource } from '../../../utils/annotationBlock';
  import { formatUploadedFileList, ResourceSelectedItem } from '../../../resource/constants';
  import PresentationResourcesList from '../../components/presentationResourcesList.vue';
  import SelectChapterStudentsModal from '../targets/selectChapterStudentsModal.vue';

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
    annotationSource: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
    annotations: {
      type: Array,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);

  const localUploadRef = ref<any>(null);
  const selectChapterStudentVisible = ref(false);
  const resourcesList = ref<any[]>([]);

  const chapterStudents = computed(() => {
    return props.chapter.chapterStudents || [];
  });

  // 资源类型过滤
  const activeResourceType = ref('all');
  const resourceTypes = [
    { key: 'all', label: '全部资源', icon: 'icon-apps' },
    { key: 'ppt', label: 'PPT课件', icon: 'icon-file' },
    { key: 'video', label: '视频', icon: 'icon-play-circle' },
    { key: 'audio', label: '音频', icon: 'icon-sound' },
    { key: 'image', label: '图片', icon: 'icon-image' },
    { key: 'game', label: '文互游戏', icon: 'icon-desktop' },
  ];

  // 上传按钮配置
  const uploadButtons = [
    { type: 'ppt', label: '上传PPT', color: 'rgb(245, 101, 36)' },
    { type: 'video', label: '上传视频', color: 'rgb(220, 53, 69)' },
    { type: 'audio', label: '上传音频', color: 'rgb(40, 167, 69)' },
    { type: 'image', label: '上传图片', color: 'rgb(0, 123, 255)' },
  ];

  // 资源库按钮配置
  const resourceLibButtons = [
    { type: 'ppt', label: 'PPT资源库', color: 'rgb(255, 193, 7)' },
    { type: 'video', label: '视频资源库', color: 'rgb(220, 53, 69)' },
    { type: 'audio', label: '音频资源库', color: 'rgb(40, 167, 69)' },
    { type: 'image', label: '图片资源库', color: 'rgb(0, 123, 255)' },
    { type: 'game', label: '文互游戏库', color: 'rgb(108, 117, 125)' },
  ];

  // 模拟资源数据
  const resourcesList = ref([
    {
      id: 1,
      name: '校园生活主题课件.pptx',
      type: 'PPT',
      size: '2.5MB',
      uploadTime: '2024-01-15',
      icon: 'icon-file',
      color: 'rgb(255, 193, 7)',
    },
    {
      id: 2,
      name: '数学认知训练.mp4',
      type: 'VIDEO',
      size: '15.2MB',
      uploadTime: '2024-01-14',
      icon: 'icon-play-circle',
      color: 'rgb(220, 53, 69)',
    },
    {
      id: 3,
      name: '儿歌背景音乐.mp3',
      type: 'AUDIO',
      size: '3.8MB',
      uploadTime: '2024-01-13',
      icon: 'icon-sound',
      color: 'rgb(40, 167, 69)',
    },
    {
      id: 4,
      name: '教学图片集合.jpg',
      type: 'IMAGE',
      size: '1.2MB',
      uploadTime: '2024-01-12',
      icon: 'icon-image',
      color: 'rgb(0, 123, 255)',
    },
  ]);

  // 过滤后的资源列表
  const filteredResources = computed(() => {
    if (activeResourceType.value === 'all') {
      return resourcesList.value;
    }
    const typeMap = {
      ppt: 'PPT',
      video: 'VIDEO',
      audio: 'AUDIO',
      image: 'IMAGE',
      game: 'GAME',
    };
    return resourcesList.value.filter((resource) => resource.type === typeMap[activeResourceType.value]);
  });

  // 计算总大小
  const totalSize = computed(() => {
    const total = filteredResources.value.reduce((sum, resource) => {
      const size = parseFloat(resource.size);
      return sum + size;
    }, 0);
    return total.toFixed(1);
  });

  // PPT课件资源库弹窗
  const pptLibraryVisible = ref(false);

  const handleUpload = (type: string) => {
    console.log('上传', type);
    // 这里实现上传逻辑
  };

  const handleOpenResourceLibrary = (type: string) => {
    if (type === 'ppt') {
      pptLibraryVisible.value = true;
    }
    console.log('打开资源库', type);
  };

  const handlePreview = (resource: any) => {
    console.log('预览', resource);
  };

  const handleDownload = (resource: any) => {
    console.log('下载', resource);
  };

  const handleDelete = (resource: any) => {
    const index = resourcesList.value.findIndex((r) => r.id === resource.id);
    if (index > -1) {
      resourcesList.value.splice(index, 1);
    }
  };
</script>

<template>
  <div class="teaching-resource-prepare">
    <!-- 标题 -->
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-semibold text-gray-800">教学资源管理</h2>
      <div class="text-sm text-gray-500"> 班级学生: 4人 </div>
    </div>

    <!-- 资源类型筛选 -->
    <div class="mb-6">
      <div class="flex flex-wrap gap-2">
        <button
          v-for="type in resourceTypes"
          :key="type.key"
          :class="[
            'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
            activeResourceType === type.key ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
          ]"
          @click="activeResourceType = type.key"
        >
          {{ type.label }}
        </button>
      </div>
    </div>

    <!-- 上传按钮组 -->
    <div class="mb-4">
      <div class="flex flex-wrap gap-3">
        <button
          v-for="btn in uploadButtons"
          :key="btn.type"
          :style="{ backgroundColor: btn.color }"
          class="px-4 py-2 rounded-lg text-white text-sm font-medium hover:opacity-90 transition-opacity flex items-center gap-2"
          @click="handleUpload(btn.type)"
        >
          <IconUpload class="w-4 h-4" />
          {{ btn.label }}
        </button>
      </div>
    </div>

    <!-- 资源库按钮组 -->
    <div class="mb-6">
      <div class="flex flex-wrap gap-3">
        <button
          v-for="btn in resourceLibButtons"
          :key="btn.type"
          class="px-4 py-2 rounded-lg text-sm font-medium border-2 transition-all hover:shadow-md flex items-center gap-2"
          :style="{
            borderColor: btn.color,
            color: btn.color,
            backgroundColor: 'transparent',
          }"
          @click="handleOpenResourceLibrary(btn.type)"
        >
          <IconCopy class="w-4 h-4" />
          {{ btn.label }}
        </button>
      </div>
    </div>

    <!-- 资源列表 -->
    <div class="bg-white rounded-lg border">
      <div class="px-4 py-3 border-b bg-gray-50 rounded-t-lg">
        <div class="flex items-center justify-between">
          <h3 class="font-medium text-gray-800"> 资源列表 ({{ filteredResources.length }}) </h3>
          <div class="text-sm text-gray-500"> 总大小: {{ totalSize }}MB </div>
        </div>
      </div>

      <div class="p-4">
        <div v-if="filteredResources.length === 0" class="text-center py-8 text-gray-500"> 暂无资源 </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <div
            v-for="resource in filteredResources"
            :key="resource.id"
            class="border rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-start gap-3">
              <div
                class="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg"
                :style="{ backgroundColor: resource.color }"
              >
                📄
              </div>
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-gray-800 truncate" :title="resource.name">
                  {{ resource.name }}
                </h4>
                <div class="text-sm text-gray-500 mt-1">
                  <div>{{ resource.type }} • {{ resource.size }}</div>
                  <div>上传于: {{ resource.uploadTime }}</div>
                </div>
              </div>
            </div>

            <div class="flex items-center justify-between mt-4 pt-3 border-t">
              <div class="flex gap-2">
                <button
                  class="p-1 text-gray-500 hover:text-blue-600 transition-colors"
                  title="预览"
                  @click="handlePreview(resource)"
                >
                  <IconEye class="w-4 h-4" />
                </button>
                <button
                  class="p-1 text-gray-500 hover:text-green-600 transition-colors"
                  title="下载"
                  @click="handleDownload(resource)"
                >
                  <IconDownload class="w-4 h-4" />
                </button>
                <button
                  class="p-1 text-gray-500 hover:text-red-600 transition-colors"
                  title="删除"
                  @click="handleDelete(resource)"
                >
                  <IconDelete class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- PPT课件资源库弹窗 -->
    <a-modal
      v-model:visible="pptLibraryVisible"
      title="PPT课件资源库"
      width="80%"
      :footer="false"
      class="ppt-library-modal"
    >
      <div class="p-4">
        <div class="flex items-center gap-4 mb-6">
          <a-input-search placeholder="搜索PPT课件..." class="flex-1" size="large" />
          <a-select placeholder="按下载量" style="width: 120px" size="large">
            <a-option value="download">按下载量</a-option>
            <a-option value="time">按时间</a-option>
            <a-option value="rating">按评分</a-option>
          </a-select>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="i in 3" :key="i" class="border rounded-lg p-4 hover:shadow-lg transition-shadow cursor-pointer">
            <div class="flex items-start gap-3 mb-4">
              <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center"> 📄 </div>
              <div class="flex-1">
                <h4 class="font-medium text-gray-800 mb-1">
                  {{ i === 1 ? '生活技能训练课件' : i === 2 ? '数学基础认知课件' : '情绪管理课件' }}
                </h4>
                <div class="text-sm text-gray-500">
                  {{ i === 1 ? '4.1MB' : i === 2 ? '3.2MB' : '2.8MB' }} •
                  {{ i === 1 ? '4.5' : i === 2 ? '4.8' : '4.0' }}分
                </div>
              </div>
            </div>

            <div class="text-sm text-gray-600 mb-3">
              {{
                i === 1
                  ? '日常生活技能训练专用课件'
                  : i === 2
                    ? '适合特殊教育学生的数学认知训练课件'
                    : '帮助学生理解和管理情绪的课件'
              }}
            </div>

            <div class="flex flex-wrap gap-2 mb-4">
              <span
                v-for="tag in i === 1
                  ? ['生活技能', '自理能力', '实用技能']
                  : i === 2
                    ? ['数学', '认知训练', '基础数学']
                    : ['情绪管理', '社交技能', '心理健康']"
                :key="tag"
                class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
              >
                {{ tag }}
              </span>
            </div>

            <div class="flex items-center justify-between text-sm text-gray-500">
              <div class="flex items-center gap-4">
                <span>👁 {{ i === 1 ? '203' : i === 2 ? '156' : '89' }}</span>
                <span>⭐ {{ i === 1 ? '4.5' : i === 2 ? '4.8' : '4.0' }}</span>
              </div>
              <span>{{ i === 1 ? '2024-01-08' : i === 2 ? '2024-01-10' : '2024-01-05' }}</span>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="scss">
  .teaching-resource-prepare {
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
  }

  .ppt-library-modal {
    :deep(.arco-modal-body) {
      max-height: 70vh;
      overflow-y: auto;
    }
  }
</style>
