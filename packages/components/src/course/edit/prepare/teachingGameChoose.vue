<script lang="ts" setup>
import { ref, computed, PropType } from 'vue';
import { IconSearch, IconSettings, IconPlay, IconStar } from '@arco-design/web-vue/es/icon';
import { AnnotationModuleSource } from '../../../utils/annotationBlock';

const props = defineProps({
  chapter: {
    type: Object,
    required: true,
  },
  course: {
    type: Object,
    required: true,
  },
  annotationSource: {
    type: Object as PropType<AnnotationModuleSource>,
    required: true,
  },
  annotations: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);

// 已选择的游戏
const selectedGames = ref([
  {
    id: 1,
    name: '情绪识别挑战',
    type: '社交技能',
    status: '已选择',
    icon: '😊'
  }
]);

// 游戏库数据
const gameLibrary = ref([
  {
    id: 1,
    name: '数字配对游戏',
    description: '通过配对数字来训练学生的数学认知和记忆能力',
    rating: 4.8,
    players: '1-4人',
    duration: '10-15分钟',
    tags: ['数学学习', '健脑'],
    icon: '🔢',
    color: 'rgb(255, 193, 7)',
    selected: false
  },
  {
    id: 2,
    name: '情绪识别挑战',
    description: '帮助学生识别和理解不同的情绪表达，提升社交技能',
    rating: 4.6,
    players: '2-6人',
    duration: '15-20分钟',
    tags: ['社交技能', '设计'],
    icon: '😊',
    color: 'rgb(255, 193, 7)',
    selected: true
  },
  {
    id: 3,
    name: '词汇拼图',
    description: '通过拼图的方式学习词汇，提升语言理解能力',
    rating: 4.7,
    players: '1-3人',
    duration: '8-12分钟',
    tags: ['语言发展', '设计'],
    icon: '🧩',
    color: 'rgb(40, 167, 69)',
    selected: false
  },
  {
    id: 4,
    name: '记忆翻牌',
    description: '经典的记忆翻牌游戏，训练学生的记忆力和专注力',
    rating: 4.5,
    players: '1-4人',
    duration: '5-10分钟',
    tags: ['记忆训练', '专注力'],
    icon: '🃏',
    color: 'rgb(220, 53, 69)',
    selected: false
  },
  {
    id: 5,
    name: '音乐节拍',
    description: '跟随音乐节拍进行互动，提升听觉感知和协调能力',
    rating: 4.9,
    players: '1-6人',
    duration: '10-15分钟',
    tags: ['音乐感知', '协调'],
    icon: '🎵',
    color: 'rgb(0, 123, 255)',
    selected: false
  },
  {
    id: 6,
    name: '颜色分类',
    description: '通过颜色分类游戏训练视觉识别和分类能力',
    rating: 4.4,
    players: '1-3人',
    duration: '8-12分钟',
    tags: ['视觉训练', '分类'],
    icon: '🎨',
    color: 'rgb(108, 117, 125)',
    selected: false
  }
]);

// 搜索和筛选
const searchKeyword = ref('');
const selectedCategory = ref('全部类别');
const selectedDifficulty = ref('全部难度');

const categories = ['全部类别', '数学学习', '社交技能', '语言发展', '记忆训练', '音乐感知', '视觉训练'];
const difficulties = ['全部难度', '简单', '中等', '困难'];

// 过滤后的游戏列表
const filteredGames = computed(() => {
  return gameLibrary.value.filter(game => {
    const matchesSearch = !searchKeyword.value || 
      game.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      game.description.toLowerCase().includes(searchKeyword.value.toLowerCase());
    
    const matchesCategory = selectedCategory.value === '全部类别' ||
      game.tags.some(tag => tag.includes(selectedCategory.value));
    
    return matchesSearch && matchesCategory;
  });
});

const handleSelectGame = (game: any) => {
  game.selected = !game.selected;
  
  if (game.selected) {
    // 添加到已选择列表
    selectedGames.value.push({
      id: game.id,
      name: game.name,
      type: game.tags[0],
      status: '已选择',
      icon: game.icon
    });
  } else {
    // 从已选择列表移除
    const index = selectedGames.value.findIndex(g => g.id === game.id);
    if (index > -1) {
      selectedGames.value.splice(index, 1);
    }
  }
};

const handleGameSettings = (game: any) => {
  console.log('游戏设置', game);
};

const handlePlayGame = (game: any) => {
  console.log('开始游戏', game);
};

const handleRemoveSelected = (game: any) => {
  const index = selectedGames.value.findIndex(g => g.id === game.id);
  if (index > -1) {
    selectedGames.value.splice(index, 1);
  }
  
  // 更新游戏库中的选择状态
  const libraryGame = gameLibrary.value.find(g => g.id === game.id);
  if (libraryGame) {
    libraryGame.selected = false;
  }
};
</script>

<template>
  <div class="teaching-game-choose">
    <!-- 已选择的游戏 -->
    <div class="mb-8">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center gap-2 mb-4">
          <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">
            ✓
          </div>
          <h3 class="font-semibold text-green-800">已选择的游戏 ({{ selectedGames.length }})</h3>
        </div>
        
        <div v-if="selectedGames.length === 0" class="text-green-600 text-center py-4">
          暂未选择任何游戏
        </div>
        
        <div v-else class="space-y-3">
          <div
            v-for="game in selectedGames"
            :key="game.id"
            class="flex items-center justify-between bg-white rounded-lg p-3 border"
          >
            <div class="flex items-center gap-3">
              <span class="text-2xl">{{ game.icon }}</span>
              <div>
                <h4 class="font-medium text-gray-800">{{ game.name }}</h4>
                <div class="flex gap-2 text-sm">
                  <span class="text-purple-600">{{ game.type }}</span>
                  <span class="text-green-600">{{ game.status }}</span>
                </div>
              </div>
            </div>
            
            <div class="flex gap-2">
              <button
                @click="handleGameSettings(game)"
                class="p-2 text-gray-500 hover:text-blue-600 transition-colors"
                title="设置"
              >
                <IconSettings class="w-4 h-4" />
              </button>
              <button
                @click="handleRemoveSelected(game)"
                class="p-2 text-gray-500 hover:text-red-600 transition-colors"
                title="移除"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 游戏库 -->
    <div class="bg-white rounded-lg border">
      <div class="px-6 py-4 border-b bg-gray-50 rounded-t-lg">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">游戏库 ({{ filteredGames.length }}个游戏)</h3>
        
        <!-- 搜索和筛选 -->
        <div class="flex flex-wrap gap-4">
          <a-input-search
            v-model="searchKeyword"
            placeholder="搜索游戏名称或描述..."
            class="flex-1 min-w-64"
          />
          <a-select
            v-model="selectedCategory"
            :options="categories.map(c => ({ label: c, value: c }))"
            style="width: 120px"
          />
          <a-select
            v-model="selectedDifficulty"
            :options="difficulties.map(d => ({ label: d, value: d }))"
            style="width: 120px"
          />
        </div>
      </div>

      <div class="p-6">
        <div v-if="filteredGames.length === 0" class="text-center py-8 text-gray-500">
          未找到匹配的游戏
        </div>
        
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="game in filteredGames"
            :key="game.id"
            class="border rounded-lg p-4 hover:shadow-lg transition-shadow relative"
          >
            <!-- 评分 -->
            <div class="absolute top-4 right-4 flex items-center gap-1 text-sm">
              <IconStar class="w-4 h-4 text-yellow-500" />
              <span class="font-medium">{{ game.rating }}</span>
            </div>
            
            <!-- 游戏图标和基本信息 -->
            <div class="flex items-start gap-3 mb-4">
              <div 
                class="w-12 h-12 rounded-lg flex items-center justify-center text-2xl"
                :style="{ backgroundColor: game.color + '20' }"
              >
                {{ game.icon }}
              </div>
              <div class="flex-1">
                <h4 class="font-semibold text-gray-800 mb-1">{{ game.name }}</h4>
                <div class="text-sm text-gray-500 space-y-1">
                  <div class="flex items-center gap-2">
                    <span>👥 {{ game.players }}</span>
                    <span>⏱ {{ game.duration }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 游戏描述 -->
            <p class="text-sm text-gray-600 mb-4 line-clamp-2">{{ game.description }}</p>
            
            <!-- 标签 -->
            <div class="flex flex-wrap gap-2 mb-4">
              <span
                v-for="tag in game.tags"
                :key="tag"
                class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
              >
                {{ tag }}
              </span>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex gap-2">
              <button
                v-if="game.selected"
                class="flex-1 py-2 px-4 bg-gray-800 text-white rounded-lg font-medium"
                disabled
              >
                已选择
              </button>
              <button
                v-else
                @click="handleSelectGame(game)"
                class="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                选择游戏
              </button>
              
              <button
                @click="handleGameSettings(game)"
                class="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                title="设置"
              >
                <IconSettings class="w-4 h-4" />
              </button>
              
              <button
                @click="handlePlayGame(game)"
                class="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                title="预览"
              >
                <IconPlay class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.teaching-game-choose {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
