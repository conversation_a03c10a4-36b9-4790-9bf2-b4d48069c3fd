<script lang="ts" setup>
  import { PropType, ref } from 'vue';
  import { AnnotationModuleSource } from '../../utils/annotationBlock';
  import TeachingResourcePrepare from './prepare/teachingResourcePrepare.vue';

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
    annotationSource: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
    annotations: {
      type: Array,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);

  const actionNav = [
    { label: '行为设计与记录', key: 'designAndRecord' },
    { label: '教学资源准备', key: 'resourcePrepare' },
    { label: '游戏库选择', key: 'gameChoose' },
  ];
  const activeNav = ref('designAndRecord');
</script>

<template>
  <div>
    <div class="h-full flex justify-center items-center mb-2">
      <!--action nav-->
      <div class="inline-flex w-full mx-8 bg-gray-50 rounded-full p-1 shadow-inner space-x-2">
        <button
          v-for="btn in actionNav"
          :key="btn.key"
          class="flex-1 text-center py-1 px-4 text-sm font-semibold rounded-full transition-all duration-200"
          :class="[
            activeNav === btn.key
              ? 'bg-white text-blue-600 shadow'
              : 'text-gray-600 hover:text-blue-500 hover:bg-white/70',
          ]"
          @click="activeNav = btn.key"
        >
          {{ btn.label }}
        </button>
      </div>
    </div>
    <!--content-->
    <div class="h-screen">
      <teaching-design-and-record
        v-if="activeNav === 'designAndRecord'"
        :chapter="chapter"
        :course="course"
        :annotation-source="annotationSource"
        :annotations="annotations"
        @save="handleSave"
        @update:annotations="handleUpdateAnnotations"
      />
      <teaching-resource-prepare
        v-if="activeNav === 'resourcePrepare'"
        :chapter="chapter"
        :course="course"
        :annotation-source="annotationSource"
        :annotations="annotations"
        @save="handleSave"
        @update:annotations="handleUpdateAnnotations"
      />
      <teaching-game-choose
        v-if="activeNav === 'gameChoose'"
        :chapter="chapter"
        :course="course"
        :annotation-source="annotationSource"
        :annotations="annotations"
        @save="handleSave"
        @update:annotations="handleUpdateAnnotations"
      />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
