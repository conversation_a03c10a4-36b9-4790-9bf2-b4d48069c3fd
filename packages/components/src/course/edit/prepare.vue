<script lang="ts" setup>
  import { PropType, ref } from 'vue';
  import { AnnotationModuleSource } from '../../utils/annotationBlock';
  import TeachingResourcePrepare from './prepare/teachingResourcePrepare.vue';
  import TeachingDesignAndRecord from './prepare/teachingDesignAndRecord.vue';
  import TeachingGameChoose from './prepare/teachingGameChoose.vue';

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
    annotationSource: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
    annotations: {
      type: Array,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);

  const actionNav = [
    { label: '行为设计与记录', key: 'designAndRecord' },
    { label: '教学资源准备', key: 'resourcePrepare' },
    { label: '游戏库选择', key: 'gameChoose' },
  ];
  const activeNav = ref('designAndRecord');

  const handleSave = () => {
    emit('save');
  };

  const handleUpdateAnnotations = (annotations: any) => {
    emit('update:annotations', annotations);
  };

  const getNavIcon = (key: string) => {
    const icons = {
      designAndRecord: '👥',
      resourcePrepare: '📚',
      gameChoose: '🎮',
    };
    return icons[key] || '📋';
  };

  const getNavIconClass = (key: string) => {
    const classes = {
      designAndRecord: 'bg-blue-100 text-blue-600',
      resourcePrepare: 'bg-green-100 text-green-600',
      gameChoose: 'bg-purple-100 text-purple-600',
    };
    return classes[key] || 'bg-gray-100 text-gray-600';
  };

  const getNavDescription = (key: string) => {
    const descriptions = {
      designAndRecord: '设计学生行为项目并记录评分',
      resourcePrepare: '管理教学资源和课件材料',
      gameChoose: '选择适合的教学游戏',
    };
    return descriptions[key] || '';
  };
</script>

<template>
  <div class="prepare-container">
    <!-- 顶部导航栏 -->
    <div class="top-nav bg-white border-b">
      <div class="flex items-center justify-between px-6">
        <h1 class="text-xl font-semibold text-gray-800">教学备课</h1>
        <div class="text-sm text-gray-500 flex items-center gap-2">
          <span>👥</span>
          <span>班级学生: 4人</span>
        </div>
      </div>
    </div>

    <!-- 三个主要功能卡片导航 -->
    <div class="nav-cards px-6 py-6 bg-gray-50">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div
          v-for="btn in actionNav"
          :key="btn.key"
          :class="[
            'nav-card cursor-pointer transition-all duration-200 transform hover:scale-105',
            activeNav === btn.key ? 'active' : '',
          ]"
          @click="activeNav = btn.key"
        >
          <div class="bg-white rounded-lg p-6 border-2 hover:shadow-lg">
            <div class="flex items-center gap-4">
              <div
                class="w-12 h-12 rounded-lg flex items-center justify-center text-2xl"
                :class="getNavIconClass(btn.key)"
              >
                {{ getNavIcon(btn.key) }}
              </div>
              <div>
                <h3 class="font-semibold text-gray-800 mb-1">{{ btn.label }}</h3>
                <p class="text-sm text-gray-500">{{ getNavDescription(btn.key) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--content-->
    <div class="content-area">
      <teaching-design-and-record
        v-if="activeNav === 'designAndRecord'"
        :chapter="chapter"
        :course="course"
        :annotation-source="annotationSource"
        :annotations="annotations"
        @save="handleSave"
        @update:annotations="handleUpdateAnnotations"
      />
      <teaching-resource-prepare
        v-if="activeNav === 'resourcePrepare'"
        :chapter="chapter"
        :course="course"
        :annotation-source="annotationSource"
        :annotations="annotations"
        @save="handleSave"
        @update:annotations="handleUpdateAnnotations"
      />
      <teaching-game-choose
        v-if="activeNav === 'gameChoose'"
        :chapter="chapter"
        :course="course"
        :annotation-source="annotationSource"
        :annotations="annotations"
        @save="handleSave"
        @update:annotations="handleUpdateAnnotations"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
  .prepare-container {
    min-height: 100vh;
    background-color: #f8f9fa;
  }

  .top-nav {
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .nav-cards {
    .nav-card {
      &.active {
        .bg-white {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }
      }
    }
  }

  .content-area {
    min-height: calc(100vh - 200px);
  }
</style>
