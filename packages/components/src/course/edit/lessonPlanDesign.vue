<script lang="ts" setup>
  import { computed, nextTick, onMounted, PropType, ref } from 'vue';
  import { EditorOrDisplay } from '@repo/rich-editor';
  import { Modal, Message } from '@arco-design/web-vue';
  import { usePrompt } from '@repo/ui/components';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import AttachmentsDisplay from '@repo/ui/components/data-display/components/attachmentsDisplay.vue';
  import AttachmentPreviewModal from '@repo/ui/components/data-display/attachmentPreviewModal.vue';
  import { cloneDeep } from 'lodash';
  import { getChapterAssessmentList } from '@repo/infrastructure/openapi/chapterAssessmentController';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AnnotationModuleSource } from '../../utils/annotationBlock';
  import AnnotatableBlock from '../../common/annotatableBlock.vue';
  import AiButton from '../../common/aiButton.vue';

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
    annotationSource: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
    annotations: {
      type: Array,
      required: true,
    },
  });

  const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:annotations']);
  const { prompt } = usePrompt();
  const sections = ['teachingPrepare', 'teachingReflection', 'lessonPrepare', 'lessonPrepareAttachments'];

  const contents = computed({
    get: () => props.chapter?.content || [],
    set: (value) => {
      emit('update:chapter', {
        ...props.chapter,
        content: {
          ...props.chapter.content,
          ...value,
        },
      });
    },
  });

  const getGlobalIndex = (sectionIndex, itemIndex) => {
    let count = 0;
    for (let i = 0; i < sectionIndex; i += 1) {
      count += contents.value[sections[i]]?.length || 0;
    }
    return count + itemIndex + 1;
  };

  const handleSave = async () => {
    emit('save');
  };

  const handleUpdateContentName = async (key, index, content) => {
    contents.value[key][index].name = await prompt({
      title: '修改教案设计名称',
      placeholder: '请输入教案设计项目名称',
      raw: content.name,
    });
    emit('save');
  };

  const handleDeleteContentItem = (key, index, content) => {
    Modal.confirm({
      title: '请确认',
      content: `确定删除该教案设计项目吗(${content.name})？`,
      onOk: () => {
        contents.value[key]?.splice(index, 1);
        emit('save');
      },
    });
  };

  const handleAddItem = async () => {
    const name = await prompt({
      title: '添加教学设计',
      placeholder: '请输入教学设计项目名称',
    });
    if (!contents.value.lessonPrepare) contents.value.lessonPrepare = [];
    contents.value.lessonPrepare.push({
      name,
      udf1: '',
    });
    emit('save');
  };

  const handleUploaded = (items) => {
    const { name, url } = items[0];
    contents.value.lessonPrepare.push({
      name,
      udf1: url,
      udf2: 'attachment',
    });
    emit('save');
  };

  const record = ref();

  const handleSetRecord = async (c) => {
    record.value = c;

    const url = c?.udf1 || c?.url;

    try {
      const response = await fetch(url, { mode: 'cors' });
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = c?.name || 'downloaded-file';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (err) {
      Message.error('下载失败:', err);
    }
  };

  const currentAttachment = ref(null);
  const attachmentVisible = ref(false);
  const handleViewAttachment = (c: any) => {
    currentAttachment.value = [c];
    attachmentVisible.value = true;
  };

  const rawResultDataMap = ref({
    pre: {},
  });
  const resultDataMap = ref({
    pre: {},
  });
  const getPostData = (type: string = 'pre') => {
    const postData = [];
    Object.keys(resultDataMap.value[type]).forEach((studentId) => {
      let node: any = {};
      node.studentId = Number(studentId);
      node.chapterId = props.chapter.id;
      node.type = type;
      node.gradeClassId = props.course.gradeClassId;
      if (rawResultDataMap.value[type][studentId]) {
        node = {
          ...rawResultDataMap.value[type][studentId],
          ...node,
        };
      } else {
        node.scores = [];
      }

      const scoresMap = resultDataMap.value[type][studentId];
      node.scores = cloneDeep(props.chapter.content?.teachingAssessCriteria || []).map((criterion) => {
        if (criterion.children?.length) {
          criterion.children.map((child) => {
            child[type] = scoresMap.scoresMap[child.id];
            child.students = [];
            return child;
          });
        } else {
          criterion[type] = scoresMap.scoresMap[criterion.id];
          criterion.students = [];
        }

        return criterion;
      });

      postData.push(node);
    });
    return postData;
  };
  // 主要是前测
  const loadResults = async (type: string = 'pre') => {
    const { data } = await getChapterAssessmentList({
      chapterId: props.chapter.id,
      type,
    });
    rawResultDataMap.value[type] = (data || []).reduce((prev: any, curr: any) => {
      prev[curr.studentId] = curr;
      return prev;
    }, {});

    const formattedData = {};

    (data || []).forEach((item) => {
      const scoresMap = {};
      (item.scores || []).forEach((score) => {
        if (score.children?.length) {
          score.children.forEach((child: any) => {
            scoresMap[child.id] = child[type];
          });
        } else {
          scoresMap[score.id] = score[type];
        }
      });

      formattedData[item.studentId] = {
        id: item.id,
        scoresMap,
      };
    });

    resultDataMap.value[type] = formattedData;
  };

  // 整合信息
  /*
    // 提取所有的非自创目标，然后查询数据库，构建树形数据，然后结合自己创建的... 多此一举，
    目标：学生&前测评分
      二级目标：学生&评分
    学生情况：年龄&障碍类型&性别
    评分细则：1-5，5-1
  * */
  // 动画
  const currentStep = ref(0);
  const animationVisible = ref(false);
  const generateLessonFinished = ref(false);
  const steps = ['1. 获取教案学生信息', '2. 获取教案目标', '3. 分析目标', '4. 生成教案'];
  const executeNext = () => {
    if (currentStep.value < steps.length) {
      currentStep.value += 1;
    }
  };
  const autoLessonPlan = async () => {
    currentStep.value = 0;
    const postData = getPostData();
    executeNext();
    const transferData = postData.map((item: any) => ({
      student: item.student,
      scores: item.scores,
      boId: item.boId,
      courseId: props.course?.id,
    }));

    let targetTemplate = [];

    if (transferData?.length) {
      const cleanNode = (node) => {
        const cleaned = {
          id: node.id,
          name: node.name,
          records: [],
        };
        if (node.children && node.children.length > 0) {
          cleaned.children = node.children.map(cleanNode);
        }
        return cleaned;
      };

      targetTemplate = transferData[0].scores.map(cleanNode);

      const insertRecord = (templateNode, scoreNode, student) => {
        delete templateNode.id;
        if (scoreNode.pre >= 0) {
          templateNode.records.push({
            id: student.id,
            name: student.name,
            numUdf1: scoreNode.pre,
          });
        }
        if (scoreNode.children && templateNode.children) {
          for (let i = 0; i < scoreNode.children.length; i += 1) {
            insertRecord(templateNode.children[i], scoreNode.children[i], student);
          }
        }
      };

      transferData.forEach((item) => {
        for (let i = 0; i < item.scores.length; i += 1) {
          insertRecord(targetTemplate[i], item.scores[i], item.student);
        }
      });
    }
    const result = {
      targetList: targetTemplate,
      teachingAssessScores: props.course?.teachingAssessScores,
    };
    console.log(result);
    executeNext();
    try {
      const { data: res } = await request('/ai/autoLessonPlan/auto', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: result,
        timeout: 1000 * 60,
      });
      console.log(res, '<==========>', result);
      executeNext();
    } finally {
      /* pretend return lessonPlan */
      contents.value.teachingPrepare.push({ udf1: 'hello world', name: 'HELLO WORLD' });
    }
    executeNext();
    animationVisible.value = false;
    generateLessonFinished.value = true;
  };

  const handleGenerateLessonPlan = async () => {
    currentStep.value = 0;
    generateLessonFinished.value = false;
    animationVisible.value = true;
    await autoLessonPlan();
  };
  onMounted(async () => {
    if (props.chapter.id) await loadResults();
  });
</script>

<template>
  <div class="w-full h-full min-h-[200px]">
    <div class="flex justify-start items-center">
      <!--手动创建-->
      <div class="relative group">
        <a-button size="mini" type="outline" class="flex items-center gap-1">
          <icon-plus class="mr-1" />
          添加教案
        </a-button>
        <div
          :class="[
            'absolute hidden group-hover:block bg-white  transform transition-all duration-300',
            'z-50 shadow-lg rounded-md p-2 min-w-[160px] border border-gray-100',
          ]"
        >
          <div class="flex flex-col space-y-2">
            <a-button size="mini" type="dashed" status="success" @click="handleAddItem">
              <icon-plus class="mr-1" />
              新建教案
            </a-button>
            <uploader :multiple="false" accept="*/*" sub-folder="" :show-file-list="false" @uploaded="handleUploaded">
              <template #uploadButton>
                <a-button size="mini" class="w-full" type="dashed" status="warning">
                  <icon-upload class="mr-1" />
                  本地上传
                </a-button>
              </template>
            </uploader>
          </div>
        </div>
      </div>
      <AiButton tooltip="AI生成" @ok="handleGenerateLessonPlan">
        <template #default>
          <span>AI</span>
        </template>
      </AiButton>
    </div>

    <!--design content-->
    <div v-for="(key, index) in sections" :key="key">
      <div
        v-for="(c, idx) in contents?.[key] || []"
        :key="idx"
        class="mt-6 border rounded max-h-[300px] overflow-auto relative"
      >
        <!-- Sticky Header -->
        <div class="sticky top-0 z-30 bg-white">
          <div class="flex items-center space-x-6 p-2">
            <div class="content-title text-base font-bold text-gray-700">
              {{ getGlobalIndex(index, idx) }}、{{ c.name?.split('.')[0] ?? c.name }}
            </div>
            <a-space class="bg-gray-100 px-4 flex space-x-2 py-2 rounded-full text-black">
              <IconEdit
                size="15"
                class="cursor-pointer hover:text-blue-500"
                @click="() => handleUpdateContentName(key, idx, c)"
              />
              <IconDelete
                size="15"
                class="cursor-pointer hover:text-red-500"
                @click="() => handleDeleteContentItem(key, idx, c)"
              />
              <div
                v-if="c?.udf2 === 'attachment' || key === 'lessonPrepareAttachments'"
                class="flex space-x-3 justify-center items-center"
              >
                <icon-fullscreen
                  size="15"
                  class="cursor-pointer hover:text-green-500"
                  @click="handleViewAttachment(c)"
                />
                <icon-download size="15" class="cursor-pointer hover:text-green-500" @click="handleSetRecord(c)" />
              </div>
            </a-space>
          </div>
        </div>

        <!-- Content Body -->
        <AttachmentsDisplay
          v-if="c?.udf2 === 'attachment' || key === 'lessonPrepareAttachments'"
          :raw="[c]"
          class="mt-2"
        />
        <editor-or-display
          v-else
          v-model="c.udf1"
          class="mt-2"
          :editor-style="{ minHeight: '180px', maxHeight: '180px' }"
          @save="handleSave"
        >
          <template #default="{ raw }">
            <annotatable-block
              v-model:annotations="filteredAnnotations"
              :annotation-module-source="{
                ...annotationModuleSource,
                paragraphId: c.paragraphId || `teaching-prepare-${idx}`,
              }"
              :related-modules="['student']"
              :initial-text="raw"
              annotation-category="调整"
            />
          </template>
        </editor-or-display>
      </div>
    </div>

    <attachment-preview-modal
      v-model="attachmentVisible"
      :current-file-index="0"
      :files-list="currentAttachment || []"
    />
    <a-modal v-model:visible="animationVisible" :title="false" :footer="false" :closable="false">
      <div class="p-6 bg-white rounded-xl shadow-xl">
        <h2 class="text-2xl font-bold text-center mb-6 text-gray-800 flex justify-center items-center gap-1">
          正在生成教案
          <span class="animate-bounce">.</span>
          <span class="animate-bounce delay-150">.</span>
          <span class="animate-bounce delay-300">.</span>
        </h2>

        <!-- 进度条 -->
        <div class="w-full bg-gray-200 rounded-full h-2 mb-6 overflow-hidden">
          <div
            class="h-full bg-gradient-to-r from-blue-400 to-green-400 transition-all duration-700"
            :style="{ width: ((currentStep + 1) / steps.length) * 100 + '%' }"
          ></div>
        </div>

        <ul class="space-y-4">
          <li
            v-for="(step, index) in steps"
            :key="index"
            :class="[
              'transform transition-all duration-700 ease-out',
              currentStep >= index ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10',
            ]"
          >
            <div
              class="flex items-center p-4 rounded-lg shadow-md bg-gradient-to-r"
              :class="[
                currentStep > index
                  ? 'from-green-100 to-green-200 ring-2 ring-green-400'
                  : currentStep === index
                    ? 'from-yellow-100 to-yellow-200 ring-2 ring-yellow-400 animate-pulse'
                    : 'from-gray-100 to-gray-200 ring-1 ring-gray-300',
              ]"
            >
              <div
                class="w-4 h-4 rounded-full mr-3"
                :class="[
                  currentStep > index
                    ? 'bg-green-500'
                    : currentStep === index
                      ? 'bg-yellow-400 animate-ping'
                      : 'bg-gray-300',
                ]"
              ></div>
              <span class="text-gray-700 font-medium text-base">{{ step }}</span>
            </div>
          </li>
        </ul>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="scss"></style>
