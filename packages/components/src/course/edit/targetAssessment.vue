<script setup lang="ts">
  import { computed, onMounted, PropType, ref, watch } from 'vue';
  import { usePrompt } from '@repo/ui/components';
  import SparkMD5 from 'spark-md5';
  import { Message } from '@arco-design/web-vue';
  import { listTableInputModal } from '@repo/ui/components/form/inputComponents';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { collapsedNameDisplay } from '@repo/infrastructure/ui';
  import {
    batchEditChapterAssessment,
    getChapterAssessmentList,
  } from '@repo/infrastructure/openapi/chapterAssessmentController';
  import { cloneDeep } from 'lodash';
  import CriterionSelectModal from '../components/criterionSelectModal.vue';
  import TeacherQuestionLibraryModal from '../../teacherQuestionLibrary/teacherQuestionLibraryModal.vue';
  import SelectTargetStudentsModal from './targets/selectTargetStudentsModal.vue';
  import SelectFromIepModal from '../components/selectFromIepModal.vue';
  import TargetIepTag from './targets/targetIepTag.vue';

  // 后6位
  const getNameHash = (name: string) => {
    const spark = new SparkMD5();
    spark.append(name);
    // add timestamp
    spark.append(`${new Date().getTime()}`);
    return spark.end().toLowerCase().slice(-6);
  };

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    course: {
      type: Object,
      required: true,
    },
    chaptersTree: {
      type: Array as PropType<Record<string, any>[]>,
      required: true,
    },
  });

  const { prompt } = usePrompt();
  const emit = defineEmits(['update:chapter', 'update:course', 'save', 'update:resultDataMap']);
  const nodes = ref<any[]>([]);
  const { loading, setLoading } = useLoading();
  const scoreType = [
    {
      label: '前测评分',
      key: 'pre',
      color: 'blue',
    },
    {
      label: '后测评分',
      key: 'after',
      color: 'green',
    },
  ];

  const selectTargetStudentVisible = ref(false);
  const currentTarget = ref<any>({});

  const handleShowSelectTargetStudent = (node: any) => {
    currentTarget.value = node;
    selectTargetStudentVisible.value = true;
  };

  const currentNode = ref<any>({});
  const questionLibVisible = ref(false);
  const teachingAssessScores = computed({
    get: () => props.course?.teachingAssessScores || [],
    set: (value) => {
      emit('update:course', {
        ...props.course,
        teachingAssessScores: value,
      });
    },
  });

  const scoreSetField = {
    key: 'teachingAssessScores',
    inputWidgetProps: {
      columns: [
        { key: 'id', label: '分值', inputWidget: 'numberInput' },
        { key: 'name', label: '显示', inputWidget: 'textInput' },
      ],
    },
  };

  const onChapterUpdated = () => {
    emit('update:chapter', {
      ...props.chapter,
      content: {
        ...props.chapter.content,
        teachingAssessCriteria: nodes.value,
      },
    });
  };

  const handleTargetUpdated = (target: any) => {
    nodes.value = nodes.value.map((node) => {
      if (node.id === target.id) {
        return target;
      }
      return node;
    });

    onChapterUpdated();
  };

  const handleNodeSelected = (node: any, form: any) => {
    nodes.value = [...nodes.value, node];
    onChapterUpdated();
    if (!props.course?.teachingAssessScores?.length) {
      if (!form.scores?.length) {
        Message.info('该量表未设置评分项目，请先手动设置');
      }
      emit('update:course', {
        ...props.course,
        teachingAssessScores:
          props.course.teachingAssessScores?.length ||
          (form.scores || []).map((item) => {
            return {
              id: item.score,
              name: item.label,
            };
          }),
      });
    }
  };

  const handleSelectFromIep = (selectedItems: any[]) => {
    nodes.value = [
      ...nodes.value,
      ...(selectedItems || []).map((item) => {
        return {
          ...item,
          id: `CTM-${getNameHash(item.name)}`,
        };
      }),
    ];
    onChapterUpdated();
  };

  const handleEditTopNode = async (node?: any) => {
    const name = await prompt({
      title: `${node ? '编辑' : '添加'}评测目标`,
      placeholder: '请输入评测目标名称',
      inputWidget: 'textarea',
      raw: node?.name,
    });

    if (!name) {
      return;
    }
    if (node) {
      node.name = name;
    } else {
      nodes.value = [...nodes.value, { name, id: `CTM-${getNameHash(name)}` }];
    }

    onChapterUpdated();
  };

  const handleDeleteTopNode = (index: number) => {
    nodes.value.splice(index, 1);
    onChapterUpdated();
  };

  const handleEditSubNode = async (node?: any, parentNode?: any) => {
    const name = await prompt({
      title: `${node ? '编辑' : '添加'}评测目标 (${parentNode.name})`,
      placeholder: '请输入二级评测目标名称',
      inputWidget: 'textarea',
      raw: node?.name,
    });

    if (!name) {
      return;
    }
    if (node) {
      node.name = name;
    } else {
      parentNode.children = parentNode.children || [];
      parentNode.children.push({ name, id: `CTM-${getNameHash(name)}` });
    }

    onChapterUpdated();
  };

  const handleDeleteSubNode = (index: number, parentNode: any) => {
    parentNode.children.splice(index, 1);
    onChapterUpdated();
  };

  const handleShowQuestionSelect = (node: any) => {
    questionLibVisible.value = true;
    currentNode.value = node;
  };

  const handleSave = () => {
    setLoading(true);
    emit('save');
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const onQuestionSelected = (questions: any[]) => {
    currentNode.value.questionIds = questions.map((item) => item.id);
    onChapterUpdated();
  };

  /* 评分 */
  const chapterAssessmentQuery = computed(() => {
    return {
      chapterId: props.chapter.id,
    };
  });
  const rawResultDataMap = ref({
    pre: {},
    after: {},
  });
  const resultDataMap = ref({
    pre: {},
    after: {},
  });

  const loadResults = async (type: string = 'pre') => {
    const { data } = await getChapterAssessmentList({
      ...chapterAssessmentQuery.value,
      type,
    });

    rawResultDataMap.value[type] = (data || []).reduce((prev: any, curr: any) => {
      prev[curr.studentId] = curr;
      return prev;
    }, {});

    const formattedData = {};

    (data || []).forEach((item) => {
      const scoresMap = {};
      (item.scores || []).forEach((score) => {
        if (score.children?.length) {
          score.children.forEach((child: any) => {
            scoresMap[child.id] = child[type];
          });
        } else {
          scoresMap[score.id] = score[type];
        }
      });

      formattedData[item.studentId] = {
        id: item.id,
        scoresMap,
      };
    });

    resultDataMap.value[type] = formattedData;
  };

  const scoresResult = computed({
    get: () => {
      const result = resultDataMap.value;

      ['pre', 'after'].forEach((type) => {
        nodes.value.forEach((item) => {
          if (item?.children?.length > 0) {
            item.children.forEach((child: any) => {
              if (item?.students?.length)
                item.students
                  ?.filter((s) => s?.id)
                  ?.forEach((s) => {
                    if (!result[type]?.[s.id]) {
                      result[type][s.id] = {
                        scoresMap: {},
                      };
                    }
                    if (!result[type]?.[s.id]?.scoresMap) {
                      result[type][s.id].scoresMap = {};
                    }
                    if (!result[type]?.[s.id]?.scoresMap?.[child.id]) {
                      result[type][s.id].scoresMap[child.id] = undefined;
                    }
                  });
            });
          } else if (item?.students?.length)
            item.students
              ?.filter((s) => s?.id)
              ?.forEach((s) => {
                if (!result[type]?.[s.id]) {
                  result[type][s.id] = {
                    scoresMap: {},
                  };
                }
                if (!result[type]?.[s.id]?.scoresMap) {
                  result[type][s.id].scoresMap = {};
                }
                if (!result[type]?.[s.id]?.scoresMap?.[item.id]) {
                  result[type][s.id].scoresMap[item.id] = undefined;
                }
              });
        });
      });

      return result;
    },
    set: (value) => {
      emit('update:resultDataMap', value);
    },
  });
  const getPostData = (type: string) => {
    const postData = [];
    Object.keys(resultDataMap.value[type]).forEach((studentId) => {
      let record: any = {};
      record.studentId = Number(studentId);
      record.chapterId = props.chapter.id;
      record.type = type;
      record.gradeClassId = props.course.gradeClassId;
      if (rawResultDataMap.value[type][studentId]) {
        record = {
          ...rawResultDataMap.value[type][studentId],
          ...record,
        };
      } else {
        record.scores = [];
      }

      const scoresMap = resultDataMap.value[type][studentId];
      record.scores = cloneDeep(props.chapter.content?.teachingAssessCriteria || []).map((criterion) => {
        if (criterion.children?.length) {
          criterion.children.map((child) => {
            child[type] = scoresMap.scoresMap[child.id];
            child.students = [];
            return child;
          });
        } else {
          criterion[type] = scoresMap.scoresMap[criterion.id];
          criterion.students = [];
        }

        return criterion;
      });

      postData.push(record);
    });
    return postData;
  };
  // 前后测目标
  const saveScore = async () => {
    try {
      const prePostData: any[] = getPostData('pre'); // 前侧
      const afterPostData: any[] = getPostData('after'); // 后测
      await Promise.all([batchEditChapterAssessment(prePostData), batchEditChapterAssessment(afterPostData)]);
      await loadResults();
      Message.success('保存成功');
    } finally {
      /**/
    }
  };

  const getAverage = (students: any, key: string, nodeId: number) => {
    if (!students?.length) return 0;
    let sum = 0;
    students.forEach((s) => {
      sum += scoresResult.value[key][s.id].scoresMap[nodeId] ?? 0;
    });
    const avg = sum / students.length;
    return Number(avg.toFixed(1));
  };
  /* 评分 */

  onMounted(async () => {
    nodes.value = props.chapter?.content?.teachingAssessCriteria || [];
    await Promise.all([loadResults(), loadResults('after')]);
  });

  watch(
    () => questionLibVisible.value,
    (value) => {
      if (!value) {
        currentNode.value = {};
      }
    },
  );
</script>

<template>
  <div>
    <!--<pre>
      {{ scoresResult }}
    </pre>-->
    <!--action bar-->
    <a-space class="justify-between items-center w-full">
      <a-space>
        <criterion-select-modal @selected="handleNodeSelected" />
        <select-from-iep-modal :selected-nodes="nodes" :course="course" @selected="handleSelectFromIep" />
        <a-button size="mini" type="outline" @click="() => handleEditTopNode()">
          <template #icon>
            <IconPlus />
          </template>
          自定义目标
        </a-button>
      </a-space>
      <a-space>
        <a-button type="primary" size="mini" :loading="loading" @click="handleSave">
          <template #icon>
            <IconSave />
          </template>
          保存
        </a-button>
        <a-button type="primary" size="mini" :loading="loading" @click.stop="saveScore">
          <template #icon>
            <IconSave />
          </template>
          保存评分
        </a-button>
        <list-table-input-modal v-model="teachingAssessScores" title="评分项设置" :schema-field="scoreSetField" />
      </a-space>
    </a-space>

    <div v-for="(node, idx) in nodes" :key="idx" class="mt-2 p-2 border-t border-slate-300">
      <!--title category-->
      <a-collapse :bordered="false">
        <a-collapse-item>
          <template #header>
            <div class="text-base font-medium flex-1 flex gap-2" @dblclick="() => handleEditTopNode(node)">
              {{ idx + 1 }}、{{ node.name }}
              <target-iep-tag v-if="node.iepTargetIds?.length" />
            </div>
          </template>
          <template #extra>
            <a-space>
              <a-popover
                :content="node.students?.map((item) => item.name)?.join('、') || '暂无参与此目标评测的学生'"
                class="w-64"
              >
                <a-button
                  type="outline"
                  status="warning"
                  size="mini"
                  @click="() => handleShowSelectTargetStudent(node)"
                >
                  <template #icon>
                    <IconUser />
                  </template>
                  <div v-if="!node.students?.length">选择学生</div>
                  <div v-else>{{ collapsedNameDisplay(node.students, { unit: '名学生' }) }}</div>
                </a-button>
              </a-popover>
              <a-tooltip content="添加下一级评测目标">
                <a-button type="outline" size="mini" @click="() => handleEditSubNode(null, node)">
                  <template #icon>
                    <IconPlus />
                  </template>
                </a-button>
              </a-tooltip>
              <a-button
                size="mini"
                :type="!node.questionIds?.length ? undefined : 'outline'"
                @click="() => handleShowQuestionSelect(node)"
              >
                <template #icon>
                  <IconQuestion />
                </template>
                试题<span v-if="node.questionIds?.length">[{{ node.questionIds?.length }}]</span>
              </a-button>
              <a-button size="mini" @click="() => handleEditTopNode(node)">
                <template #icon>
                  <IconEdit />
                </template>
              </a-button>
              <a-popconfirm
                type="warning"
                content="确定要删除这个评测目标吗？其下所有子目标将一并被删除！"
                style="width: 270px"
                @ok="() => handleDeleteTopNode(idx)"
              >
                <a-button size="mini">
                  <template #icon>
                    <IconDelete />
                  </template>
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>

          <template #default>
            <div v-if="node?.students?.length && !node?.children" class="p-4">
              <div class="text-lg font-semibold mb-4">学生评分：</div>
              <div class="flex justify-start gap-6">
                <div v-for="type in scoreType" :key="type.key" class="w-1/2">
                  <div class="border rounded-lg p-4" :class="` border-${type.color}-200 bg-${type.color}-50 `">
                    <div :class="`text-${type.color}-600 font-semibold mb-4`">📝 {{ type.label }}</div>
                    <div v-for="student in node.students" :key="student.name" class="mb-2">
                      <div class="flex items-center">
                        <span class="w-1/4">🧒 {{ student.name }}</span>
                        <a-radio-group
                          v-model="scoresResult[type.key][student.id].scoresMap[node.id]"
                          type="button"
                          :class="`flex gap-2 !bg-${type.color}-50`"
                        >
                          <a-tooltip
                            v-for="(score, dex) in course.teachingAssessScores"
                            :key="dex"
                            :content="score.name"
                          >
                            <a-radio
                              :value="score.id"
                              :class="[
                                '!border',
                                '!rounded-lg',
                                `!border-${type.color}-200`,
                                `hover:!bg-white`,
                                'hover:!text-black',
                              ]"
                            >
                              {{ score.id }}
                              <span>{{ `（${score?.name}）` }}</span>
                            </a-radio>
                          </a-tooltip>
                        </a-radio-group>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex justify-between items-center mt-6">
                <div class="text-sm">
                  前测平均：
                  <span class="text-blue-600">{{ getAverage(node.students, 'pre', node.id) }}</span>
                  ，后测平均：
                  <span class="text-green-600">{{ getAverage(node.students, 'after', node.id) }}</span>
                </div>
              </div>
            </div>
            <div v-else-if="node.children" class="ml-2">
              <div v-for="(child, i) in node.children" :key="i" class="px-2 my-1 py-1">
                <a-collapse>
                  <a-collapse-item>
                    <template #header>
                      <div class="flex-1" @dblclick="() => handleEditSubNode(child, node)">
                        {{ `${idx + 1}.${i + 1}、${child.name}` }}
                      </div>
                    </template>
                    <template #extra>
                      <a-space>
                        <a-button
                          size="mini"
                          :type="!child.questionIds?.length ? undefined : 'outline'"
                          @click.stop="() => handleShowQuestionSelect(child)"
                        >
                          <template #icon>
                            <IconQuestion />
                          </template>
                          试题<span v-if="child.questionIds?.length">[{{ child.questionIds?.length }}]</span>
                        </a-button>
                        <a-button size="mini" @click.stop="() => handleEditSubNode(child, node)">
                          <template #icon>
                            <IconEdit />
                          </template>
                        </a-button>
                        <a-popconfirm
                          type="warning"
                          content="确定要删除这个评测目标吗？"
                          style="width: 250px"
                          @ok.stop="() => handleDeleteSubNode(i, node)"
                        >
                          <a-button size="mini">
                            <template #icon>
                              <IconDelete />
                            </template>
                          </a-button>
                        </a-popconfirm>
                      </a-space>
                    </template>

                    <template #default>
                      <div v-if="node?.students?.length" class="p-4">
                        <div class="text-lg font-semibold mb-4">学生评分：</div>
                        <div class="flex justify-start gap-6">
                          <div v-for="type in scoreType" :key="type.key" class="w-1/2">
                            <div
                              class="border rounded-lg p-4"
                              :class="` border-${type.color}-200 bg-${type.color}-50 `"
                            >
                              <div :class="`text-${type.color}-600 font-semibold mb-4`">📝 {{ type.label }}</div>
                              <div v-for="student in node.students" :key="student.name" class="mb-2">
                                <div class="flex items-center">
                                  <span class="w-1/4">🧒 {{ student.name }}</span>
                                  <a-radio-group
                                    v-model="scoresResult[type.key][student.id].scoresMap[child.id]"
                                    type="button"
                                    :class="`flex gap-2 !bg-${type.color}-50`"
                                  >
                                    <a-tooltip
                                      v-for="(score, dex) in course.teachingAssessScores"
                                      :key="dex"
                                      :content="score.name"
                                    >
                                      <a-radio
                                        :value="score.id"
                                        :class="[
                                          '!border',
                                          '!rounded-lg',
                                          `!border-${type.color}-200`,
                                          `hover:!bg-white`,
                                          'hover:!text-black',
                                          scoresResult[type.key][student.id].scoresMap[child.id] === Number(score?.id)
                                            ? `font-bold`
                                            : ' !text-black',
                                        ]"
                                      >
                                        {{ score.id }}
                                        <span v-if="false">{{ `（${score?.name}）` }}</span>
                                      </a-radio>
                                    </a-tooltip>
                                  </a-radio-group>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="flex justify-between items-center mt-6">
                          <div class="text-sm">
                            前测平均：
                            <span class="text-blue-600">
                              {{ getAverage(node.students, 'pre', child.id) }}
                            </span>
                            ， 后测平均：
                            <span class="text-green-600">{{ getAverage(node.students, 'after', child.id) }}</span>
                          </div>
                        </div>
                      </div>
                      <a-empty v-else description="暂无学生请先设置" />
                    </template>
                  </a-collapse-item>
                </a-collapse>
              </div>
            </div>
          </template>
        </a-collapse-item>
      </a-collapse>
    </div>
    <a-empty v-if="!nodes.length" description="暂无评测目标，请先添加" />

    <teacher-question-library-modal
      v-if="currentNode?.id"
      v-model:visible="questionLibVisible"
      v-model:selected="currentNode.questionIds"
      :course="course"
      mode="select"
      @update:selected="onQuestionSelected"
    />

    <select-target-students-modal
      v-if="selectTargetStudentVisible"
      v-model:visible="selectTargetStudentVisible"
      v-model:target="currentTarget"
      :chapter="chapter"
      @update:target="handleTargetUpdated"
    />
  </div>
</template>

<style scoped lang="scss">
  .node-item {
    &:hover {
      background-color: #f9f9f9;
    }
  }
  .pre-btn {
    background-color: #3d98ff !important;
    color: white !important;
  }
  .after-btn {
    background-color: #62ff83 !important;
    color: white !important;
  }
</style>
