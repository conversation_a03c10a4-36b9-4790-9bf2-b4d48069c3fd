<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { EditorOrDisplay } from '@repo/rich-editor';
  import AttachmentsDisplay from '@repo/ui/components/data-display/components/attachmentsDisplay.vue';
  import AnnotatableBlock from '../../common/annotatableBlock.vue';
  import { AnnotationItem, AnnotationModuleSource } from '../../utils/annotationBlock';

  const props = defineProps({
    chapter: {
      type: Object,
      required: true,
    },
    currentDirectory: {
      type: String,
      required: true,
    },
    showAssessment: {
      type: Boolean,
      default: true,
    },
    assessResultsViaStudent: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    assessResultsViaCriteriaMap: {
      type: Object,
      default: () => ({}),
    },
    annotations: {
      type: Array as PropType<AnnotationItem[]>,
      default: () => [],
    },
    annotationModule: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'refer', 'update:annotations']);
  const annotationsList = computed({
    get: () => props.annotations,
    set: (value) => {
      emit('update:annotations', value);
    },
  });

  const currentCriteria = ref<any>(null);
  const viewByCriteriaVisible = ref<any>(false);

  const handleViewAssessmentViaCriterion = async (criteria: any) => {
    currentCriteria.value = criteria;
    viewByCriteriaVisible.value = true;
  };
  const sections = ['teachingPrepare', 'teachingReflection', 'lessonPrepare', 'lessonPrepareAttachments'];

  const contents = computed({
    get: () => props.chapter?.content || [],
  });
  const getGlobalIndex = (sectionIndex, itemIndex) => {
    let count = 0;
    for (let i = 0; i < sectionIndex; i += 1) {
      count += contents.value[sections[i]]?.length || 0;
    }
    return count + itemIndex + 1;
  };
</script>

<template>
  <div>
    <div class="text-lg font-medium">{{ chapter.number }} {{ currentDirectory }} </div>
    <a-divider :margin="10" />

    <!--教案设计-->
    <div class="mb-4">
      <div class="text-base font-medium"> 一、教案设计 </div>
      <div v-for="(key, index) in sections" :key="key">
        <div v-for="(c, idx) in contents?.[key] || []" :key="idx" class="mt-6 rounded overflow-auto relative">
          <!-- Sticky Header -->
          <div class="sticky top-0 z-30 bg-white">
            <div class="flex items-center space-x-6 p-2">
              <div class="content-title text-base font-bold text-gray-700">
                {{ getGlobalIndex(index, idx) }}、{{ c.name?.split('.')[0] ?? c.name }}
              </div>
            </div>
          </div>

          <!-- Content Body -->
          <AttachmentsDisplay
            v-if="c?.udf2 === 'attachment' || key === 'lessonPrepareAttachments'"
            :raw="[c]"
            class="mt-2"
          />
          <annotatable-block
            v-else-if="c.udf1?.trim()"
            v-model:annotations="annotationsList"
            class="mt-1 text-gray-600"
            :initial-text="c.udf1"
            :related-modules="['student']"
            :annotation-module-source="{
              ...annotationModule,
              paragraphId: c.paragraphId || `teaching-prepare-${idx}`,
            }"
            annotation-category="调整"
          />
        </div>
      </div>
    </div>

    <div v-if="false">
      <div class="mb-4">
        <div class="text-base font-medium"> 一、教学准备 </div>
        <div v-for="(item, idx) in chapter.content.teachingPrepare" :key="idx" class="mx-2">
          <div class="mt-2 text-base"> {{ idx + 1 }}、{{ item.name }} </div>
          <!--<rich-text-display class="mt-1 text-gray-600" :raw="item.udf1" />-->
          <annotatable-block
            v-if="item.udf1?.trim()"
            v-model:annotations="annotationsList"
            class="mt-1 text-gray-600"
            :initial-text="item.udf1"
            :related-modules="['student']"
            :annotation-module-source="{
              ...annotationModule,
              paragraphId: item.paragraphId || `teaching-prepare-${idx}`,
            }"
            annotation-category="调整"
          />
        </div>
        <a-empty v-if="!chapter.content.teachingPrepare?.length" content="暂无教学准备" />
      </div>
      <div class="mb-4">
        <div class="text-base font-medium"> 二、备课安排 </div>
        <div v-for="(item, idx) in chapter.content.lessonPrepare" :key="idx" class="mx-2">
          <div class="mt-2 text-base"> {{ idx + 1 }}、{{ item.name }} </div>
          <!--<rich-text-display class="mt-1 text-gray-600" :raw="item.udf1" />-->
          <annotatable-block
            v-if="item.udf1?.trim()"
            v-model:annotations="annotationsList"
            class="mt-1 text-gray-600"
            :initial-text="item.udf1"
            :related-modules="['student']"
            :annotation-module-source="{
              ...annotationModule,
              paragraphId: item.paragraphId || `teaching-plan-${idx}`,
            }"
            annotation-category="调整"
          />
        </div>
        <a-empty v-if="!chapter.content.lessonPrepare?.length" content="暂无备课安排" />
      </div>
    </div>

    <div v-if="showAssessment" class="mb-4">
      <div class="text-base font-medium"> 三、学生评测 </div>
      <a-tabs class="mt-2">
        <a-tab-pane key="viaStudent" title="按学生查看">
          <a-table
            row-key="id"
            class="m-2"
            :data="assessResultsViaStudent"
            size="mini"
            :default-expand-all-rows="true"
            :pagination="false"
          >
            <template #columns>
              <a-table-column title="目标" data-index="name" />
              <!--<a-table-column title="前测结果" data-index="pre" />
              <a-table-column title="后测结果" data-index="after" />-->
              <a-table-column title="前测结果" data-index="pre">
                <template #cell="{ record }">
                  <span v-if="!record.children">{{ record?.pre || '-' }}</span>
                </template>
              </a-table-column>
              <a-table-column title="后测结果" data-index="after">
                <template #cell="{ record }">
                  <span v-if="!record.children">{{ record?.after || '-' }}</span>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="viaCriteria" title="按评测目标查看">
          <a-table
            class="m-2"
            :data="chapter.content.teachingAssessCriteria"
            size="mini"
            :default-expand-all-rows="true"
            :pagination="false"
          >
            <template #columns>
              <a-table-column title="目标" data-index="name" />
              <a-table-column title="评测结果" data-index="criteria">
                <template #cell="{ record }">
                  <a-button
                    v-if="!record.children?.length"
                    size="mini"
                    @click="() => handleViewAssessmentViaCriterion(record)"
                    >查看评测结果</a-button
                  >
                </template>
              </a-table-column>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
      <a-empty v-if="!chapter.content.teachingAssessCriteria?.length" content="暂无教学目标" />
    </div>
    <div v-else class="mb-4">
      <div class="text-base font-medium"> 三、目标设定 </div>
      <a-table
        class="m-2"
        :data="chapter.content.teachingAssessCriteria"
        size="mini"
        :default-expand-all-rows="true"
        :pagination="false"
      >
        <template #columns>
          <a-table-column title="目标" data-index="name" />
        </template>
      </a-table>
    </div>
    <div v-if="false" class="mb-4">
      <div class="text-base font-medium">四、教学反思</div>
      <div v-for="(item, index) in chapter?.content?.teachingReflection" :key="item.id">
        <div class="flex justify-start mt-2 ml-4"> {{ `${index + 1}、` }}<div v-html="item?.udf1"></div> </div>
      </div>
    </div>

    <a-modal v-model:visible="viewByCriteriaVisible" :title="currentCriteria?.name" :width="600">
      <a-table :data="assessResultsViaCriteriaMap[currentCriteria?.id]?.students" :pagination="false">
        <template #columns>
          <a-table-column title="班级" data-index="gradeClass" />
          <a-table-column title="学生" data-index="name" />
          <!--<a-table-column title="前测结果" data-index="pre" />
          <a-table-column title="后测结果" data-index="after" />-->
          <a-table-column title="前测结果" data-index="pre">
            <template #cell="{ record }">
              <span v-if="!record.children">{{ record?.pre || '-' }}</span>
            </template>
          </a-table-column>
          <a-table-column title="后测结果" data-index="after">
            <template #cell="{ record }">
              <span v-if="!record.children">{{ record?.after || '-' }}</span>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<style scoped lang="scss"></style>
