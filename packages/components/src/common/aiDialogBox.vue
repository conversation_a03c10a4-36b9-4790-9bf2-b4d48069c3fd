<script setup lang="ts">
  import { computed, defineProps, defineEmits, ref, PropType, watch, onMounted, nextTick } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { marked } from 'marked';
  import AttachmentsDisplay from '@repo/ui/components/data-display/components/attachmentsDisplay.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    streamCall: {
      type: Boolean,
      default: false,
      required: false,
    },
    response: {
      type: Object as PropType<any>,
    },
    sessionId: {
      type: String,
      default: 'default',
    },
    isPC: {
      type: Boolean,
      default: true,
    },
  });

  const emits = defineEmits(['update:visible', 'send', 'stop', 'reset']);
  const chatContainer = ref<HTMLElement | null>(null);

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emits('update:visible', value),
  });

  const isReply = ref(false);
  const isLoading = ref(false);
  const drawerVisible = ref(false);
  const msg = ref<string>('请分析如下文件并总结：');
  const uploadedFiles = ref<any[]>([]);
  const currentFile = ref(null);

  const loadChatHistory = () => {
    const history = localStorage.getItem(`chat_history_${props.sessionId}`);
    return history ? JSON.parse(history) : [];
  };

  const chatCatch = ref<any[]>(loadChatHistory());

  const saveChatHistory = () => {
    localStorage.setItem(`chat_history_${props.sessionId}`, JSON.stringify(chatCatch.value));
  };
  const reset = () => {
    isReply.value = false;
    msg.value = '';
    uploadedFiles.value = [];
  };

  const load = () => {
    chatCatch.value = loadChatHistory();
  };
  // 初始化时加载历史记录
  onMounted(() => {
    chatCatch.value = loadChatHistory();
  });

  const handleSend = () => {
    if (!msg.value || msg.value === '') {
      Message.clear('top');
      Message.error('发送消息不能为空');
      return;
    }
    isReply.value = true;
    // isLoading.value = true;
    chatCatch.value.push({ role: 'user', msg: msg.value });
    chatCatch.value.push({ role: 'robot', msg: '' });
    emits('send', msg.value);
    saveChatHistory();
  };

  const handleStop = () => {
    isReply.value = false;
    isLoading.value = false;
    emits('stop');
  };

  const handleCopy = (message: any) => {
    navigator.clipboard.writeText(message).then(() => {
      Message.clear('top');
      Message.success('已复制');
    });
  };

  const clearChatHistory = () => {
    chatCatch.value = [];
    localStorage.removeItem(`chat_history_${props.sessionId}`);
    localStorage.removeItem(`chat_history_`);
    localStorage.removeItem(`chat_history_default`);
    localStorage.removeItem(`behavior_chat_sessionId`);
    reset();
    emits('reset');
  };
  const handleScrollToBottom = async () => {
    await nextTick();

    if (chatContainer.value) {
      chatContainer.value.scrollTo({
        top: chatContainer.value.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  const setFileList = (list: any[]) => {
    uploadedFiles.value = list;
  };
  const getFileList = () => {
    return uploadedFiles.value ?? [];
  };
  const handleSetCurrentFile = (file: any) => {
    currentFile.value = file;
    drawerVisible.value = true;
  };
  const removeFile = (index) => {
    uploadedFiles.value.splice(index, 1);
  };
  watch(
    () => props.response,
    async (newVal: any) => {
      if (newVal) chatCatch.value[chatCatch.value.length - 1].msg = newVal;
      isLoading.value = false;
      await handleScrollToBottom();
    },
    { deep: true, immediate: true },
  );

  defineExpose({
    saveChatHistory,
    reset,
    setFileList,
    getFileList,
    load,
  });
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    fullscreen
    :closable="false"
    :draggable="true"
    v-bind="$attrs"
    :body-style="{ padding: 0, margin: 0, height: '100vh' }"
  >
    <template #title>
      <slot name="title">
        <div class="flex justify-between items-center w-full">
          <slot name="boxTitle">
            <span class="font-bold">{{ '行为分析小诸葛' }}</span>
          </slot>
          <div class="flex justify-center space-x-2">
            <a-button
              size="mini"
              type="outline"
              class="clearBtn"
              shape="round"
              status="danger"
              @click="clearChatHistory"
            >
              <template #icon>
                <icon-delete />
              </template>
              清空记录
            </a-button>

            <a-button
              v-if="isPC"
              size="mini"
              type="outline"
              shape="round"
              class="closeBtn"
              @click="modalVisible = false"
            >
              <template #icon>
                <icon-close />
              </template>
              关闭对话
            </a-button>
          </div>
        </div>
      </slot>
    </template>
    <slot name="content">
      <a-spin :loading="isLoading" class="w-full h-full">
        <div ref="chatContainer" class="w-full h-full overflow-auto px-4 py-2 bg-white rounded space-y-4">
          <div
            v-for="(message, index) in chatCatch"
            :key="index"
            class="flex items-start"
            :class="message.role === 'user' ? 'justify-end mr-2 md:mr-[10%]' : 'justify-start ml-2 md:ml-[10%]'"
          >
            <!-- 助手头像 -->
            <div
              v-if="message.role !== 'user' && isPC"
              :class="[
                'w-9 h-9 mr-2 rounded-full bg-[#e0f0ff] border border-[#c6e0ff]',
                'shadow flex items-center justify-center text-[#0468b5]',
              ]"
            >
              <icon-robot size="18" />
            </div>

            <!--dialog-->
            <div
              class="max-w-[100%] md:max-w-[70%] rounded-lg shadow text-sm relative group"
              :class="
                message.role === 'user'
                  ? 'bg-sky-400 text-white rounded-br-none px-3 md:px-6  shadow-lg'
                  : 'bg-gray-50 text-gray-800 rounded-bl-none px-3 py-3 md:px-[80px] md:py-[40px]'
              "
            >
              <icon-copy
                v-if="message.role !== 'user'"
                :class="[
                  'absolute right-1 top-1 cursor-pointer opacity-0',
                  'group-hover:opacity-100 transition-opacity duration-200',
                ]"
                @click="handleCopy(message.msg)"
              />
              <icon-loading v-if="!message?.msg && index === chatCatch.length - 1 && isReply" />
              <div v-else class="markdown-content" v-html="marked(message?.msg || '')" />
            </div>

            <!-- 用户头像 -->
            <div
              v-if="message.role === 'user' && isPC"
              :class="[
                'w-9 h-9 ml-2 rounded-full bg-gradient-to-br from-sky-400 to-blue-600',
                'shadow-md text-white text-sm font-bold flex items-center justify-center',
              ]"
            >
              U
            </div>
          </div>
          <div class="absolute bottom-6 right-4 md:right-10">
            <slot name="moreAction">
              <div class="flex justify-center items-center space-x-2">
                <div
                  class="rounded-full cursor-pointer text-black flex flex-justify-center items-center p-2"
                  :class="isPC ? 'bg-gray-100' : 'bg-gray-100/30 border border-gray-400'"
                  @click="handleScrollToBottom"
                >
                  <icon-down size="20" />
                </div>
              </div>
            </slot>
          </div>
        </div>
      </a-spin>
    </slot>

    <!--    <template #footer>
      <slot name="footer">
        <div class="bg-gray-50 rounded-[8px] border mx-2 md:mx-[10%] relative">
          <div class="min-h-20 max-h-40 w-full bg-red-100 absolute -top-20"></div>
          <a-textarea
            v-model="msg"
            class="max-h-[50px] inputBox"
            style="border-radius: 8px; border: none"
            placeholder="问一问ai"
            @keydown.enter="handleSend"
          />
          <div class="flex justify-end items-center mb-3 mr-2 space-x-2">
            <slot name="toolBar"></slot>
            <a-tooltip :content="isReply ? '等待回复' : '发送'">
              <a-button
                v-if="!isReply"
                shape="round"
                style="background-color: #d5ecff; color: #0468b5"
                @click="handleSend"
              >
                <template #icon>
                  <icon-send size="15" />
                </template>
              </a-button>
              <a-button v-else shape="round" style="background-color: #ffdddf; color: #ff121e" @click="handleStop">
                <template #icon>
                  <icon-record-stop />
                </template>
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </slot>
    </template>-->
    <template #footer>
      <slot name="footer">
        <div class="bg-gray-50 rounded-[8px] border mx-2 md:mx-[10%] relative">
          <!-- 上传文件展示区域 -->
          <transition name="fade">
            <div
              v-if="uploadedFiles.length"
              :class="[
                'grid auto-rows-max grid-flow-row gap-2 px-3 pt-4 pb-1',
                'max-h-40 overflow-y-auto place-content-center',
              ]"
              style="grid-template-columns: repeat(auto-fill, 220px)"
            >
              <div
                v-for="(file, index) in uploadedFiles"
                :key="index"
                :class="[
                  'bg-white border border-gray-200 rounded-md px-3 py-1 text-sm',
                  'flex items-center justify-between shadow-sm hover:shadow transition-all',
                  'group h-10 w-[220px] hover:cursor-pointer',
                ]"
                @click="handleSetCurrentFile(file)"
              >
                <a-tooltip :content="file?.name">
                  <div :class="['flex items-center']">
                    <icon-file :class="['mr-1 text-blue-500']" />
                    <span :class="['truncate max-w-[140px] block text-gray-800']">
                      {{ file?.name }}
                    </span>
                  </div>
                </a-tooltip>

                <icon-close
                  :class="[
                    'ml-2 text-gray-400 hover:text-red-500 cursor-pointer',
                    'opacity-0 group-hover:opacity-100 transition',
                  ]"
                  @click.stop="removeFile(index)"
                />
              </div>
            </div>
          </transition>

          <!-- 聊天输入框 -->
          <a-textarea
            v-model="msg"
            class="max-h-[50px] inputBox"
            style="border-radius: 8px; border: none"
            placeholder="问一问 AI"
            @keydown.enter="handleSend"
          />

          <!-- 工具栏 & 发送按钮 -->
          <div class="flex justify-end items-center mb-3 mr-2 space-x-2">
            <slot name="toolBar"></slot>
            <a-tooltip :content="isReply ? '等待回复' : '发送'">
              <a-button
                v-if="!isReply"
                shape="round"
                style="background-color: #d5ecff; color: #0468b5"
                @click="handleSend"
              >
                <template #icon>
                  <icon-send size="15" />
                </template>
              </a-button>
              <a-button v-else shape="round" style="background-color: #ffdddf; color: #ff121e" @click="handleStop">
                <template #icon>
                  <icon-record-stop />
                </template>
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </slot>
    </template>
    <a-drawer v-if="drawerVisible" v-model:visible="drawerVisible" width="50%" :footer="false">
      <template #title>
        <span>
          {{ currentFile?.name }}
        </span>
      </template>
      <AttachmentsDisplay :raw="[currentFile]" class="mt-2 bg-red-100" />
    </a-drawer>
  </a-modal>
</template>

<style lang="scss" scoped>
  @tailwind base;
  @tailwind components;
  @tailwind utilities;

  .inputBox {
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
    resize: none;

    &:hover {
      background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
    }
  }

  .clearBtn {
    font-size: 12px;
    color: red;
    border: #ff7a81 1px solid;
    padding: 0 14px;

    &:hover {
      color: white;
      border: #ff444e 1px solid;
      background-color: #ff535e;
    }
  }

  .closeBtn {
    font-size: 12px;
    color: black;
    border: gray 1px solid;
    padding: 0 14px;

    &:hover {
      color: white;
      border: gray 1px solid;
      background-color: #333333;
      font-weight: 700;
    }
  }

  ::v-deep .markdown-content {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
      font-weight: bold;
    }

    h1 {
      font-size: 2em;
    }
    h2 {
      font-size: 1.5em;
    }
    h3 {
      font-size: 1.17em;
    }
    h4 {
      font-size: 1em;
    }
    h5 {
      font-size: 0.83em;
    }
    h6 {
      font-size: 0.67em;
    }
    p {
      margin: 1em 0;
      line-height: 1.5;
    }
    ul,
    ol {
      margin: 1em 0;
      padding-left: 2em;
    }
    a {
      color: #0366d6;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
    pre {
      padding: 16px;
      overflow: auto;
      background-color: rgba(27, 31, 35, 0.05);
      border-radius: 4px;
    }
    code {
      border-radius: 3px;
      padding: 0.2em 0.4em;
    }
    blockquote {
      border-left: 4px solid #dfe2e5;
      color: #6a737d;
      padding: 0 1em;
      margin: 0 0 1em 0;
    }
    table {
      border-collapse: collapse;
      margin: 1em 0;
      width: 100%;
      th,
      td {
        border: 1px solid #dfe2e5;
        padding: 6px 13px;
      }
      th {
        background-color: #f6f8fa;
      }
    }
  }
</style>
