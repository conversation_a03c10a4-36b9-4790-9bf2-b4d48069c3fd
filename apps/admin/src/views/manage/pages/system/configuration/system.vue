<!--<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { useConfigureStore } from '@repo/infrastructure/store';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import SendConfiguration from '@/views/manage/pages/system/configuration/sendConfiguration.vue';
  import {
    checkboxInput,
    radioInput,
    selectInput,
    switchInput,
    textareaInput,
    textInput,
  } from '@repo/ui/components/form/inputComponents';
  import CustomConfigurationWidget from '@/views/manage/components/configuration/customConfigurationWidget.vue';
  import { Message } from '@arco-design/web-vue';

  const activeTab = ref('sendEdu');
  const configureStore = useConfigureStore();
  const loading = ref(false);
  const configureMaps = ref(configureStore.configureMap);
  const schema = ref(null);
  const currentConfigureItems = computed(() => {
    return (schema.value.fieldsMap.configureItem.inputWidgetProps.options || []).filter(
      (item: any) => item.group === activeTab.value,
    );
  });
  const currentConfigureData = ref({});

  /**
   * Text,
   *     Textarea,
   *     Select,
   *     Checkbox,
   *     Radio,
   *     Custom
   */
  const widgetsMap = {
    Text: textInput,
    Textarea: textareaInput,
    Select: selectInput,
    Checkbox: checkboxInput,
    Radio: radioInput,
    Switch: switchInput,
  };

  const handleTabChange = (key) => {
    activeTab.value = key;
    const items = Object.values(configureMaps.value).filter((item: any) => item.configureGroup === key);
    currentConfigureData.value = items.reduce((prev: any, current: any) => {
      prev[current.configureItem] = current.value;
      return prev;
    }, {});
  };

  const handleSave = async () => {
    loading.value = true;
    try {
      configureMaps.value = await configureStore.updateConfigure(currentConfigureData.value);
      Message.success('保存成功');
    } finally {
      loading.value = false;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/common/configure');
  });
</script>

<template>
  <div v-if="schema" class="bg-white">
    <a-tabs v-model:active-key="activeTab" @change="handleTabChange">
      <a-tab-pane key="sendEdu" title="送教配置">
        <send-configuration />
      </a-tab-pane>
      <a-tab-pane
        v-for="group in schema.fieldsMap.configureGroup.inputWidgetProps.options"
        :key="group.value"
        :title="group.label"
      >
        <div class="px-4">
          <a-form v-if="currentConfigureItems.length && activeTab === group.value" auto-label-width>
            <a-form-item v-for="(configItem, idx) in currentConfigureItems" :key="idx" :label="configItem.label">
              <custom-configuration-widget
                v-if="configItem.widget === 'Custom'"
                v-model="currentConfigureData[configItem.value]"
                :config-item="configItem.value"
                :schema-field="{
                  key: configItem.value,
                  options: configItem.options,
                }"
              />
              <component
                :is="widgetsMap[configItem.widget]"
                v-else
                v-model="currentConfigureData[configItem.value]"
                :placeholder="configItem.remark"
                :schema-field="{
                  key: configItem.value,
                  options: configItem.options,
                }"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" size="mini" :loading="loading" @click="handleSave">
                <template #icon>
                  <IconSave />
                </template>
                保存
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style scoped lang="scss"></style>-->

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { useConfigureStore } from '@repo/infrastructure/store';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import SendConfiguration from '@/views/manage/pages/system/configuration/sendConfiguration.vue';
  import {
    checkboxInput,
    radioInput,
    selectInput,
    switchInput,
    textareaInput,
    textInput,
  } from '@repo/ui/components/form/inputComponents';
  import CustomConfigurationWidget from '@/views/manage/components/configuration/customConfigurationWidget.vue';
  import { Message } from '@arco-design/web-vue';

  const activeTab = ref('sendEdu');
  const configureStore = useConfigureStore();
  const loading = ref(false);
  const configureMaps = ref(configureStore.configureMap);
  const schema = ref(null);
  const currentConfigureItems = computed(() => {
    return (schema.value.fieldsMap.configureItem.inputWidgetProps.options || []).filter(
      (item: any) => activeTab.value === item.group,
    );
  });
  const currentConfigureData = ref({});

  const widgetsMap = {
    Text: textInput,
    Textarea: textareaInput,
    Select: selectInput,
    Checkbox: checkboxInput,
    Radio: radioInput,
    Switch: switchInput,
  };

  const handleTabChange = (key) => {
    activeTab.value = key?.[0] ?? 'sendEdu';
    const items = Object.values(configureMaps.value).filter((item: any) => item.configureGroup === key?.[0]);
    currentConfigureData.value = items.reduce((prev: any, current: any) => {
      prev[current.configureItem] = current.value;
      return prev;
    }, {});
  };

  const handleSave = async () => {
    loading.value = true;
    try {
      configureMaps.value = await configureStore.updateConfigure(currentConfigureData.value);
      Message.success('保存成功');
    } finally {
      loading.value = false;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/common/configure');
  });
</script>

<template>
  <div v-if="schema" class="bg-white">
    <a-collapse accordion :bordered="false" @change="handleTabChange">
      <a-collapse-item key="sendEdu" header="送教配置">
        <send-configuration />
      </a-collapse-item>
      <a-collapse-item
        v-for="group in schema.fieldsMap.configureGroup.inputWidgetProps.options"
        :key="group.value"
        :header="group.label"
      >
        <div class="px-4">
          <a-form v-if="currentConfigureItems.length && activeTab === group.value" auto-label-width>
            <a-form-item v-for="(configItem, idx) in currentConfigureItems" :key="idx" :label="configItem.label">
              <custom-configuration-widget
                v-if="configItem.widget === 'Custom'"
                v-model="currentConfigureData[configItem.value]"
                :config-item="configItem.value"
                :schema-field="{
                  key: configItem.value,
                  options: configItem.options,
                }"
              />
              <component
                :is="widgetsMap[configItem.widget]"
                v-else
                v-model="currentConfigureData[configItem.value]"
                :placeholder="configItem.remark"
                :schema-field="{
                  key: configItem.value,
                  options: configItem.options,
                }"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" size="mini" :loading="loading" @click="handleSave">
                <template #icon>
                  <IconSave />
                </template>
                保存
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </a-collapse-item>
    </a-collapse>
  </div>
</template>

<style scoped lang="scss"></style>
