<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import TeacherSelect from '@repo/components/org/teacherSelect.vue';
  import { computed, onMounted, ref, watch } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { cloneDeep } from 'lodash';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getAdminClientNature } from '@repo/components/utils/utils';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import CustomTransferInput from '@repo/ui/components/customInput/customTransferInput.vue';

  const schema = ref(null);

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);
  const teacherListVisible = ref(false);
  const studentListVisible = ref(false);
  const currentRow = ref(null);
  const tableRef = ref(null);
  const { loading, setLoading } = useLoading();
  const teacherSelectRef = ref(null);

  const queryParams = {
    schoolNature: menuInfo.app.label,
    sort: '-id',
    orgNature: getAdminClientNature(),
  };
  const studentList = ref([]);
  const filterStudentList = ref([]);
  const targetStudent = ref([]);
  const originalTarget = ref([]);
  const deletedFromOriginalTarget = ref([]);
  const originalTeacher = ref([]);
  const deletedTeacher = ref([]);
  const selectedGradClass = ref();
  const selectedGrad = ref();
  const currentStudentName = ref();

  const handleRowAction = async (action: Record<string, any>, record: any) => {
    currentRow.value = cloneDeep(record);
    switch (action.key) {
      case 'teacherListMaintain':
        teacherListVisible.value = true;
        originalTeacher.value = currentRow.value?.teacherList.map((teacher) => teacher.id) || [];
        break;
      case 'studentListMaintain':
        filterStudentList.value = currentRow.value.studentList.map((student: any) => {
          return { value: student.id, label: student.name };
        });
        targetStudent.value = currentRow.value.studentList.map((item) => item.id);
        originalTarget.value = cloneDeep(targetStudent.value);
        studentListVisible.value = true;
        break;
      default:
        break;
    }
  };

  const resetData = () => {
    currentRow.value = null;
    selectedGrad.value = null;
    selectedGradClass.value = null;
    currentStudentName.value = null;
    targetStudent.value = [];
  };
  const handleClose = () => {
    teacherListVisible.value = false;
    studentListVisible.value = false;
    resetData();
  };

  const handleSelectTeacher = (id, teacher: any) => {
    deletedTeacher.value = deletedTeacher.value.filter((item) => item !== teacher.id);
    const exists = currentRow.value.teacherList.find((item) => item.id === teacher.id);
    if (exists) {
      Message.error('该教师已存在');
      return;
    }
    currentRow.value.teacherList.push({
      id: teacher.id,
      name: teacher.name,
      fixed: false,
      courses: [],
      remark: '',
    });
  };
  const currentRowTeacherIds = computed(() => {
    return currentRow.value.teacherList.map((teacher) => teacher.id);
  });
  const handleSelectAll = (isSelected: boolean, ids: number[], list: any) => {
    if (isSelected) {
      /* 全选 全部添加进去，然后如果已经存在直接跳过 */
      list.forEach((teacher) => {
        if (!currentRowTeacherIds.value.includes(teacher.id))
          currentRow.value.teacherList.push({
            id: teacher.id,
            name: teacher.name,
            fixed: false,
            courses: [],
            remark: '',
          });
      });
    } else {
      /* 取消全选 去除存在于list 里面的数据 且不包含于 originalTeacher || 删除的问题得考虑下  not enough time */
      currentRow.value.teacherList = currentRow.value.teacherList.filter((item) =>
        originalTeacher.value.includes(item.id),
      );
    }
  };

  const deleteTeacher = (record) => {
    deletedTeacher.value.push(record.id);
    currentRow.value.teacherList = currentRow.value.teacherList.filter((item) => item.id !== record.id);
  };

  const handleDeleteTeacher = async () => {
    try {
      await request(`/resourceRoom/subgroup/removeTeacherFromGroup/${currentRow.value.id}`, {
        method: 'delete',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: deletedTeacher.value,
      });
      Message.success('教师移除成功');
    } finally {
      /**/
    }
  };

  const handleSaveTeachersList = async () => {
    setLoading(true);
    try {
      if (deletedTeacher.value.length > 0) await handleDeleteTeacher();

      await request(`/resourceRoom/subgroup/${currentRow.value.id}`, {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...currentRow.value,
          managerUserIds: currentRow.value.teacherList
            .filter((item) => {
              return item.fixed === true;
            })
            .map((item) => {
              return item.id;
            }),
        },
      });
      Message.success('修改任课教师成功');
      tableRef.value.handleLoadData();
      return true;
    } catch (e) {
      Message.error('修改任课教师失败');
      return false;
    } finally {
      resetData();
      setLoading(false);
    }
  };
  const boStore = useCommonStore({
    api: '/resourceRoom/student',
    queryParams: {
      ...queryParams,
    },
  });

  const studentsList = ref();

  const handleRemoveStudentFromGroup = async () => {
    await request(`/resourceRoom/subgroup/removeStudentFromGroup/${currentRow.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
      data: deletedFromOriginalTarget.value,
    });
  };

  const handleSaveStudentList = async () => {
    if (!studentsList.value) {
      studentListVisible.value = false;
      resetData();
      return;
    }
    try {
      if (deletedFromOriginalTarget.value.length) await handleRemoveStudentFromGroup();
      setLoading(true);
      await request(`/resourceRoom/subgroup/${currentRow.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: {
          ...currentRow.value,
          studentList: studentsList.value.map((item) => {
            return {
              id: item.id,
              name: item.name,
            };
          }),
        },
      });
      tableRef.value.handleLoadData();
    } finally {
      resetData();
      setLoading(false);
    }
  };

  const handleTransferChange = (val: any) => {
    studentsList.value = studentList.value.filter((item) => targetStudent.value.includes(item.id));
    const keep = originalTarget.value.filter((item) => targetStudent.value.includes(item)) || [];
    deletedFromOriginalTarget.value = originalTarget.value.filter((item) => !keep.includes(item));
  };
  const allStudentList = computed(() => {
    return studentList.value.map((student) => ({ label: student.name, value: student.id }));
  });
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/subgroup');
    studentList.value = await boStore.getList();
  });

  watch(
    () => targetStudent.value,
    () => {
      handleTransferChange();
    },
  );
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    ref="tableRef"
    module-name="分组"
    :schema="schema"
    :default-query-params="queryParams"
    :visible-columns="['name', 'teacherList', 'createdDate']"
    @row-action="handleRowAction"
  >
    <a-modal
      v-model:visible="teacherListVisible"
      :width="800"
      title="分组教师管理"
      :on-before-ok="handleSaveTeachersList"
      :ok-loading="loading"
      :render-to-body="false"
      @close="handleClose"
    >
      <teacher-select
        v-if="currentRow"
        ref="teacherSelectRef"
        show-search
        :show-role="true"
        :multiple="true"
        :default-query-params="{
          // branchOffice: currentRow.grade?.school?.branchOfficeId,
          orgNature: currentRow?.orgNature,
        }"
        @change="handleSelectTeacher"
        @select-all="handleSelectAll"
      />
      <a-table :data="currentRow?.teacherList || []" class="mt-2" :pagination="false">
        <template #columns>
          <a-table-column title="教师" data-index="name"></a-table-column>
          <a-table-column title="负责人" data-index="fixed">
            <template #cell="{ record }">
              <a-switch v-model="record.fixed" size="small" type="round" />
            </template>
          </a-table-column>
          <a-table-column title="备注" data-index="remark">
            <template #cell="{ record }">
              <a-input v-model="record.remark" size="mini" />
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-button type="text" size="mini" @click="() => deleteTeacher(record)"> 删除</a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-modal>
    <a-modal
      v-model:visible="studentListVisible"
      fullscreen
      title="学生维护"
      :on-before-ok="handleSaveStudentList"
      :render-to-body="false"
      @close="handleClose"
    >
      <a-form auto-label-width>
        <a-form-item label="请选择学生">
          <customTransferInput
            v-if="studentListVisible"
            v-model="targetStudent"
            :data="allStudentList"
            class="w-full"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </table-with-modal-form>
</template>

<style scoped lang="scss">
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
</style>
