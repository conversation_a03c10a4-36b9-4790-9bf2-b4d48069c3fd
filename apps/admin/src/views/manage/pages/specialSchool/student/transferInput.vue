<template>
  <div class="space-y-4">
    <a-input-search
      v-model="searchQuery"
      placeholder="搜索"
      allow-clear
      size="small"
      class="w-1/2"
      @search="handleSearch"
      @clear="handleSearchClear"
    />

    <div class="flex min-h-[400px] space-x-4">
      <!-- 源列表 -->
      <div
        class="w-1/2 border border-gray-200 rounded-xl shadow-sm max-h-[500px] overflow-y-auto flex flex-col bg-white"
      >
        <div class="flex justify-between items-center p-2 sticky top-0 bg-white z-10 border-b">
          <span class="text-sm text-gray-600 font-medium">待选 {{ filteredSourceItems.length }} 条</span>
          <a-tooltip content="全选">
            <a-button size="mini" type="outline" shape="circle" icon="select-all" @click="selectAll">
              <template #icon>
                <icon-select-all />
              </template>
            </a-button>
          </a-tooltip>
        </div>
        <a-checkbox-group v-model="selectedSourceItems" class="flex flex-col gap-2 p-2">
          <div
            v-for="item in filteredSourceItems"
            :key="item.value"
            class="flex items-center p-2 hover:bg-gray-50 rounded-lg transition"
          >
            <a-checkbox :value="item.value" />
            <span class="ml-2 truncate text-sm text-gray-700">{{ item.label }}</span>
          </div>
          <div v-if="filteredSourceItems.length === 0" class="text-gray-400 text-sm text-center py-6">暂无数据</div>
        </a-checkbox-group>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-col justify-center items-center space-y-4 w-10">
        <icon-right
          size="20"
          class="cursor-pointer hover:text-blue-500 transition"
          :class="{ 'text-gray-300': selectedSourceItems.length === 0 }"
          @click="addToTarget"
        />
        <icon-left
          size="20"
          class="cursor-pointer hover:text-blue-500 transition"
          :class="{ 'text-gray-300': selectedTargetItems.length === 0 }"
          @click="removeFromTarget"
        />
      </div>

      <!-- 目标列表 -->
      <div
        class="w-1/2 border border-gray-200 rounded-xl shadow-sm max-h-[500px] overflow-y-auto flex flex-col bg-white"
      >
        <div class="flex justify-between items-center p-2 sticky top-0 bg-white z-10 border-b">
          <span class="text-sm text-gray-600 font-medium">已选 {{ modelValue.length }} 条</span>
          <a-tooltip content="清空">
            <a-button size="mini" type="outline" status="danger" shape="circle" icon="delete" @click="clearAll">
              <template #icon> <icon-delete /> </template>
            </a-button>
          </a-tooltip>
        </div>
        <a-checkbox-group v-model="selectedTargetItems" class="flex flex-col gap-2 p-2">
          <div
            v-for="item in targetItems"
            :key="item.value"
            class="flex justify-between items-center p-2 hover:bg-gray-50 rounded-lg transition"
          >
            <div class="flex items-center">
              <a-checkbox :value="item.value" />
              <span class="ml-2 truncate text-sm text-gray-700">{{ item.label }}</span>
            </div>
            <icon-close
              size="14"
              class="cursor-pointer text-gray-400 hover:text-red-500 transition"
              @click="removeItem(item.value)"
            />
          </div>
          <div v-if="modelValue.length === 0" class="text-gray-400 text-sm text-center py-6">暂无选中数据</div>
        </a-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';

  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
      validator: (value) => {
        return value.every((item) => item.value && item.label);
      },
    },
    modelValue: {
      type: Array,
      default: () => [],
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const searchQuery = ref('');
  const selectedSourceItems = ref([]);
  const selectedTargetItems = ref([]);

  // 过滤源数据
  const filteredSourceItems = computed(() => {
    const query = searchQuery.value.toLowerCase();
    return props.data.filter(
      (item) =>
        !props.modelValue.includes(item.value) &&
        (item.label.toLowerCase().includes(query) || (item.className && item.className.toLowerCase().includes(query))),
    );
  });

  // 目标列表项
  const targetItems = computed(() => {
    const query = searchQuery.value.toLowerCase();
    return props.data.filter(
      (item) =>
        props.modelValue.includes(item.value) &&
        (item.label.toLowerCase().includes(query) || (item.className && item.className.toLowerCase().includes(query))),
    );
  });

  // 全选源列表
  const selectAll = () => {
    selectedSourceItems.value = filteredSourceItems.value.map((item) => item.value);
  };

  // 清空目标列表
  const clearAll = () => {
    emit('update:modelValue', []);
  };

  // 添加到目标列表
  const addToTarget = () => {
    if (selectedSourceItems.value.length === 0) return;
    const newValue = [...new Set([...props.modelValue, ...selectedSourceItems.value])];
    emit('update:modelValue', newValue);
    selectedSourceItems.value = [];
  };

  // 从目标列表移除选中项
  const removeFromTarget = () => {
    if (props.modelValue.length === 0) return;
    const newValue = props.modelValue.filter((value) => !selectedTargetItems.value.includes(value));
    emit('update:modelValue', newValue);
    selectedTargetItems.value = [];
  };

  // 移除单个项
  const removeItem = (value) => {
    const newValue = props.modelValue.filter((v) => v !== value);
    emit('update:modelValue', newValue);
  };

  // 搜索处理
  const handleSearch = () => {
    // 可以添加额外的搜索逻辑
  };

  // 清除搜索
  const handleSearchClear = () => {
    searchQuery.value = '';
  };
</script>
