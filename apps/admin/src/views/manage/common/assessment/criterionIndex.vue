<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { useRoute, useRouter } from 'vue-router';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { axiosInstance, request } from '@repo/infrastructure/request';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';

  defineProps({
    moduleName: {
      type: String,
      default: '评估',
    },
  });

  const schema = ref(null);
  const route = useRoute();
  const router = useRouter();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);
  const importVisible = ref(false);
  const currentRow = ref(null);
  const uploaded = ref();
  const { loading: importLoading, setLoading: setImportLoading } = useLoading();

  const handleBatchExport = async (recordIds: any) => {
    Message.info('正在导出量表，请稍后在右上角下载');
    try {
      const { data: res } = await request(`/evaluation/customCriterionResult/batchExport`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: recordIds,
      });
    } finally {
      /**/
    }
  };

  const handleRowAction = (action: any, record?: any) => {
    switch (action.key) {
      case 'adjustDetail':
        router.push(`/manage/${menuInfo.app.key}/assessment/adjustCriterionDetail?id=${record.id}`);
        break;
      case 'importDetail':
        currentRow.value = record;
        importVisible.value = true;
        break;
      case 'batchExport':
        handleBatchExport([record?.id]);
        break;
      default:
        break;
    }
  };

  const handleImportClose = () => {
    importVisible.value = false;
    currentRow.value = null;
  };

  const handleUpload = async (option) => {
    setImportLoading(true);
    const { fileItem } = option;
    const formData = new FormData();
    formData.set('file', fileItem.file);

    try {
      await axiosInstance.post(`/evaluation/customCriterion/importCriterion/${currentRow.value.id}`, formData, {
        method: 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      importVisible.value = false;
      currentRow.value = null;

      Message.success('评估明细导入成功');
    } finally {
      uploaded.value = null;
      setImportLoading(false);
    }
  };

  const handleUploadDrop = async (e) => {
    const f = e.dataTransfer.files[0];
    await handleUpload({
      fileItem: {
        file: f,
      },
    });
  };

  onMounted(async () => {
    const store = useSchoolCourseStore();
    await store.getSchoolCourses();
    schema.value = await SchemaHelper.getInstanceByDs('/evaluation/customCriterion');
  });
</script>

<template>
  <div>
    <table-with-modal-form
      v-if="schema"
      :modal-width="1000"
      :module-name="moduleName"
      :schema="schema"
      :modal-form-property="{ modalWidth: 900 }"
      :default-query-params="{ sort: '-id', orgNature: menuInfo.app.label }"
      :default-edit-value="{ orgNature: menuInfo.app.label }"
      :visible-columns="[
        'type',
        'schoolCourseId',
        'category',
        'name',
        'applicableScope',
        'usedStudentCount',
        'maxScore',
        'description',
        'createdDate',
      ]"
      @row-action="handleRowAction"
    />
    <a-modal
      v-model:visible="importVisible"
      :hide-cancel="true"
      :ok-loading="importLoading"
      title="导入评估明细"
      ok-text="完成"
      @close="handleImportClose"
    >
      <div> {{ currentRow?.name }} </div>
      <a-upload
        v-if="importVisible && currentRow"
        v-model="uploaded"
        :show-file-list="false"
        draggable
        :custom-request="handleUpload"
        class="mt-2"
        accept="*.xlsx,*.xls"
        @drop="handleUploadDrop"
      />
    </a-modal>
  </div>
</template>
