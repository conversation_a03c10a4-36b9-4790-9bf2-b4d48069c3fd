<script setup lang="ts">
  import { 
    IconRefresh, 
    IconSearch, 
    IconBarChart, 
    IconFile, 
    IconEdit,
    IconUser,
    IconCheckCircle,
    IconClockCircle,
    IconExclamationCircle,
    IconArchive
  } from '@arco-design/web-vue/es/icon';
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import Detail from '@repo/components/d2dEduShare/detail.vue';
  import { UserAvatarListDisplay } from '@repo/ui/components/data-display/components';

  import { Message, Modal } from '@arco-design/web-vue';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import Analysis from '@repo/components/assessment/analysis.vue';
  import supervisionResultListModal from '@/views/manage/components/supervision/supervisionResultListModal.vue';
  import { randomHighChromaColor, randomColor } from '@repo/ui/components/utils/randomColor';

  const props = defineProps({
    type: {
      type: String,
      default: 'evaluate',
    },
    branchList: {
      type: Array,
    },
    teacherList: {
      type: Array,
    },
  });
  const size = 'mini';
  const sendVersion = {
    simple: 1,
    archive: 2,
  };
  // subject filed && genre &&
  const filters = computed(() => {
    if (props.type === 'evaluate') return ['school', 'period', 'subject', 'times', 'type'];

    if (props.type === 'Plan') return ['school', 'period'];

    return ['school', 'period', 'subject'];
  });
  const periodsList = getPeriodsList({
    from: 2023,
    wholeIsNeeded: false,
  });
  const menuStore = useUserMenuStore();
  const date2Period = (date?: Date | string): string => {
    const now = date ? new Date(date) : new Date();
    const month = now.getMonth() + 1;
    const isAutumn = month >= 9 || month <= 1; // 2
    const year = now.getFullYear() - (isAutumn && month >= 1 && month <= 2 ? 1 : 0);
    const season = isAutumn ? '秋' : '春';
    return `${year}年${season}季学期`;
  };
  const defaultValue = {
    selectAll: -1,
  };
  const params = ref<any>({
    statisticsType: props.type,
    school: defaultValue.selectAll,
    subject: defaultValue.selectAll,
    times: defaultValue.selectAll,
    type: defaultValue.selectAll,
    current: 1,
    page: 1,
    pageSize: 20,
    period: date2Period(),
    nature: menuStore.getCurrentOrgNature(),
    // submitStatus: 'Draft',
    // statisticsStatus: 'Enable',
  });

  const emits = defineEmits(['search']);

  // const teacherMap = computed(() => {
  //   if (!props.teacherList) {
  //     return {};
  //   }
  //   return props.teacherList.reduce(
  //     (map, teacher: any) => {
  //       map[teacher?.id] = teacher;
  //       return map;
  //     },
  //     {} as { [id: number]: any },
  //   );
  // });

  const commonColumns = [
    { title: '序号', slotName: 'num', width: 100, align: 'center' },
    { dataIndex: 'student.name', title: '学生' },
    { dataIndex: 'student.disorders', title: '障碍类型' },
    { dataIndex: 'submitStatus', title: '状态', slotName: 'submitStatus' },
  ];
  const columns = {
    evaluate: [
      ...commonColumns,
      { dataIndex: 'type', title: '类型', slotName: 'type' },
      { dataIndex: 'criterionName', title: '量表', slotName: 'criterionName' },
      { dataIndex: 'timesLabel', title: '次数' },
      { dataIndex: 'evaluationDate', title: '时间' },
      // { dataIndex: 'assessmentTeachers', title: '老师' },
      { slotName: 'assessmentTeachers', title: '老师' },
      { title: '操作', slotName: 'operate', width: 220, align: 'center' },
    ],
    iep: [
      ...commonColumns,
      { dataIndex: 'gradePeriod', title: '学期' },
      { dataIndex: 'meetingDate', title: '制定时间' },
      // { dataIndex: 'participantIds', title: '参会人员' },
      { dataIndex: 'guardianConfirmed', title: '家长确认', slotName: 'guardianConfirmed' },
      { dataIndex: 'createdBy.name', title: '老师' },
      { title: '操作', slotName: 'operate', width: 220, align: 'center' },
    ],
    isp: [
      ...commonColumns,
      { dataIndex: 'gradePeriod', title: '学期' },
      { dataIndex: 'meetingDate', title: '制定时间' },
      // { dataIndex: 'participantIds', title: '参会人员' },
      { dataIndex: 'guardianConfirmed', title: '家长确认', slotName: 'guardianConfirmed' },
      { dataIndex: 'createdBy.name', title: '老师' },
      { title: '操作', slotName: 'operate', width: 220, align: 'center' },
    ],
    send: [
      ...commonColumns,
      { dataIndex: 'period', title: '学期' },
      { dataIndex: 'createdDate', title: '创建时间' },
      { dataIndex: 'personInCharge', title: '负责人' },
      { dataIndex: 'createdBy.name', title: '创建人' },
      { title: '操作', slotName: 'operate', width: 220, align: 'center' },
    ],
    rehabilitation: [
      ...commonColumns,
      { dataIndex: 'gradePeriod', title: '学期' },
      { dataIndex: 'createdDate', title: '创建时间' },
      { dataIndex: 'createdBy.name', title: '老师' },
      { title: '操作', slotName: 'operate', width: 220, align: 'center' },
    ],
  };
  const modules = {
    evaluate: [
      {
        title: '评估概况',
        key: 'overview',
        children: [
          { label: '总评估', key: 'total', style: 'text-black' },
          { label: '已完成', key: 'finished', style: 'text-green-500', statisticsStatus: 'Enable' },
          { label: '已归档', key: 'archived', style: 'text-blue-500', submitStatus: 'Submitted' },
          { label: '进行中', key: 'inProgress', style: 'text-amber-500', submitStatus: 'Draft' },
        ],
      },
      {
        title: '评估详情',
        key: 'details',
        children: [
          { label: '已评估', key: 'finished', style: 'text-green-500', statisticsStatus: 'Enable' },
          { label: '未评估', key: 'inProgress', style: 'text-red-500' },
        ],
      },
      {
        title: '评估进度',
        key: 'progress',
        children: [
          { label: '参与教师', key: 'teachersNum', style: 'text-sky-500' },
          { label: '量表份数', key: 'scaleNum', style: 'text-purple-500' },
        ],
      },
    ],

    Plan: [
      {
        title: '个别化教育计划',
        progress: true,
        key: 'iep',
        progressValue: 0.8,
        children: [
          { label: '未开始', key: 'notStarted', style: 'text-red-500' },
          { label: '进行中', key: 'inProgress', style: 'text-amber-500', submitStatus: 'Draft' },
          { label: '已归档', key: 'finished', style: 'text-green-500', submitStatus: 'Submitted' },
        ],
      },
      {
        title: '支持计划',
        progress: true,
        key: 'isp',
        progressValue: 0.8,
        children: [
          { label: '未开始', key: 'notStarted', style: 'text-red-500' },
          { label: '进行中', key: 'inProgress', style: 'text-amber-500', statisticsStatus: 'Enable' },
          { label: '已归档', key: 'finished', style: 'text-green-500', statisticsStatus: 'Enable' },
        ],
      },
      {
        title: '送教计划',
        progress: true,
        key: 'send',
        progressValue: 0.8,
        children: [
          { label: '未开始', key: 'notStarted', style: 'text-red-500' },
          { label: '进行中', key: 'inProgress', style: 'text-amber-500', statisticsStatus: 'Enable' },
          { label: '已归档', key: 'finished', style: 'text-green-500', statisticsStatus: 'Enable' },
        ],
      },
      {
        title: '康复计划',
        progress: true,
        key: 'rehabilitation',
        progressValue: 0.8,
        children: [
          { label: '未开始', key: 'notStarted', style: 'text-red-500' },
          { label: '进行中', key: 'inProgress', style: 'text-amber-500', statisticsStatus: 'Enable' },
          { label: '已归档', key: 'finished', style: 'text-green-500', statisticsStatus: 'Enable' },
        ],
      },
    ],

    implement: [
      {
        title: '教案统计',
        key: 'teachingPlan',
        icon: IconBarChart,
        children: [
          { label: '总教案', key: 'total', style: 'text-black', statisticsStatus: 'Enable' },
          { label: '已提交', key: 'finished', style: 'text-green-500', statisticsStatus: 'Enable' },
          { label: '未提交', key: 'inProgress', style: 'text-red-500', statisticsStatus: 'Enable' },
        ],
      },
      {
        title: '教案类型',
        key: 'lessonPlanType',
        icon: IconFile,
        children: [
          { label: '原创教案', key: 'originalNum', style: 'text-purple-500' },
          { label: '引用教案', key: 'referenceNum', style: 'text-amber-500' },
        ],
      },
      {
        title: '人均教案',
        icon: IconEdit,
        key: 'average',
        children: [{ label: '人均教案数', key: 'average', style: 'text-sky-500' }],
      },
    ],
  };
  const courseList = ref([]);
  const data = ref<any>({});
  const dataList = ref<any>([]);
  const filterList = ref([]);
  const subjectNames = computed({
    get: () => {
      if (props.type === 'implement') {
        const result = courseList.value
          .filter((item) => item.branchOfficeId === params.value.school)
          .map((item) => ({ label: item.name, value: item.id }));
        result.unshift({ label: '全部', value: -1 });
        return result;
      }
      const result = courseList.value.map((item) => ({
        label: item?.name,
        value: item?.id,
      }));
      result.unshift({ label: '全部', value: -1 });
      return result;
    },
    set: (val) => {
      dataList.value = val;
    },
  });

  const times = computed(() => {
    return data.value?.times || [];
  });
  const genre = computed(() => {
    const result = [
      { label: '全部', value: -1 },
      { label: '量表', value: 'Criterion' },
      { label: '诊断', value: 'Diagnosis' },
    ];
    return result;
  });

  const gradeList = computed(() => {
    if (props.type === 'implement') {
      const gradeSet = new Set<number>();
      return dataList.value
        .filter((item) => {
          if (gradeSet.has(item.grade.id)) {
            return false;
          }
          gradeSet.add(item.grade.id);
          return true;
        })
        .map((item) => ({
          label: item.grade.name,
          value: item.grade.id,
        }));
    }
    return [];
  });

  const gradeClassList = computed(() => {
    if (props.type === 'implement') {
      return dataList.value
        .filter((item) => item.grade.id === params.value.grade)
        .map((item) => ({
          label: item?.name,
          value: item.id,
        }));
    }
    return [];
  });

  const resetFilters = () => {
    params.value.subject = null;
    params.value.times = null;
    params.value.type = null;
  };

  // 加载其它信息 去除全部值，然后前端过滤就行
  const handleBorOrPeriodChange = async () => {
    resetFilters();
    let url = '';

    if (props.type === 'evaluate') {
      url = '/evaluation/customCriterionResult';
    } else if (props.type === 'implement') {
      url = '/resourceRoom/gradeClass';
    } else return;

    try {
      const { data: res } = await request(url, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          branchOfficeId: params.value?.school,
          ...params.value,
        },
      });
      dataList.value = res.items;
      // filterList.value = res.items;
    } catch (e) {
      /**/
    }
  };

  const handleSubjectChange = () => {
    // params.value.times = null;
    // filterList.value = dataList.value.filter((item) => item[key] === val);
  };

  const handleTimesChange = (val: any, key: string) => {
    filterList.value = dataList.value.filter((item) => item[key] === val);
  };

  const total = ref(0);
  const currentIndex = ref();

  // const loadPlans = async (ids: any, url: string) => {
  //   try {
  //     const { data: res } = await request(url, {
  //       baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  //       method: 'put',
  //       data: ids,
  //     });
  //     filterList.value = res.items;
  //   } catch (e) {
  //     console.error(e);
  //   }
  // };

  const currentCard = ref();
  const activeEvaluateCard = ref('total'); // 新增：跟踪当前选中的评估卡片

  const isReady = ref(false);
  const handleSearch = async () => {
    isReady.value = false;
    Message.loading('数据加载中...');
    emits('search', params);
    if (props.type === 'implement') {
      // 实施
      const { data: res } = await request('/teacher/teachingSupervisionStatistics', {
        method: 'put',
        params: params.value,
      });
      data.value = res;
      dataList.value = res?.raw || [];
      Message.clear('top');
    } else {
      // 另外两个
      const { data: res } = await request('/resourceCenter/supervisionStatistics', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: params.value,
      });
      data.value = res;
      if (props.type === 'evaluate') {
        dataList.value = res?.raw || [];
        filterList.value = res?.raw || [];
        total.value = res?.totalData || res?.raw?.length || 0;
      } else {
        const key = currentCard.value || 'iep';
        currentCard.value = key;
        currentIndex.value = currentIndex.value || 0;

        dataList.value = res[key]?.raw || [];
        filterList.value = res[key]?.raw || [];
        total.value = res[key]?.totalData || 0;
      }
      Message.clear('top');
    }
    await nextTick(() => {
      isReady.value = true;
    });
  };

  const schema = ref();
  const rawSchema = ref();
  const schemaCatch = ref<any>({});
  const currentRecord = ref();

  const handleSelectCard = async (index: number, item: any) => {
    if (props.type !== 'Plan' || index === currentIndex.value) return;
    currentCard.value = item.key;
    currentIndex.value = index;

    schema.value = null;
    if (schemaCatch.value?.[item.key]) {
      schema.value = schemaCatch.value?.[item.key];
    } else {
      let url = '';
      if (props.type === 'Plan') {
        switch (item.key) {
          case 'iep':
            url = '/resourceRoom/individualizedEducation';
            break;
          case 'isp':
            url = '/resourceRoom/individualizedSupportPlan';
            break;
          case 'send':
            url = '/resourceRoom/sendEducationPlan';
            break;
          case 'rehabilitation':
            url = '/rehabilitation/rehabilitationPlan';
            break;
          default:
            break;
        }
      } else if (props.type === 'evaluate') {
        url = '/evaluation/customCriterionResult';
      }
      const sc = await SchemaHelper.getInstanceByDs(url);
      schema.value = sc;
      schemaCatch.value[item.key] = sc;
    }

    await handleSearch();
    filterList.value = data.value?.[item.key]?.raw || [];
    total.value = data.value?.[item.key]?.totalData || filterList.value?.length || 0;
  };

  const handleReset = () => {
    params.value.school = defaultValue.selectAll;
    params.value.subject = defaultValue.selectAll;
    params.value.times = defaultValue.selectAll;
    params.value.type = defaultValue.selectAll;
    params.value.current = 1;
    params.value.page = 1;
    params.value.pageSize = 20;
    params.value.period = date2Period();
  };

  const handlePageChange = async (val: number) => {
    params.value.current = val;
    params.value.page = val;
    await handleSearch();
  };

  const handlePageSizeChange = async (val: number) => {
    params.value.pageSize = val;
    await handleSearch();
  };

  const pagination = computed(() => {
    return {
      current: params.value.current,
      total: total.value,
      pageSize: params.value.pageSize,
      page: params.value.page,
      showTotal: true,
      pageSizeOptions: [2, 20, 30, 50, 100, 200],
      showPageSize: true,
      size: 'small' as const,
    };
  });
  const loadSchoolCourse = async () => {
    const { data: res } = await request('/teacher/schoolCourse', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    });
    courseList.value = res.items;
  };
  const bodyStyle = {
    height: '100%',
    overflow: 'auto',
  };

  const viewVisible = ref(false);

  const resultListVisible = ref(false);

  const handleView = async (record: any) => {
    currentRecord.value = record;
    viewVisible.value = true;
  };

  const handleReject = async (record: any) => {
    const buildData = {
      module: currentCard.value,
      id: record.id,
    };
    Modal.confirm({
      title: '确认驳回？',
      content: '驳回后需老师再次提交',
      onOk: async () => {
        await request('/resourceCenter/supervisionStatistics/reject', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'put',
          data: buildData,
        });
        await handleSearch();
      },
    });
  };

  const handleSelect = async (item, children) => {
    // 设置活跃卡片状态
    if (children?.statisticsStatus === 'Enable') {
      activeEvaluateCard.value = 'finished';
    } else if (children?.submitStatus === 'Draft') {
      activeEvaluateCard.value = 'inProgress';
    } else if (children?.submitStatus === 'Submitted') {
      activeEvaluateCard.value = 'archived';
    }

    params.value.submitStatus = null;
    params.value.statisticsStatus = null;
    params.value.page = 1;
    params.value.pageSize = 20;

    if (children?.submitStatus) {
      params.value.submitStatus = children?.submitStatus;
    }
    if (children?.statisticsStatus) {
      params.value.statisticsStatus = children?.statisticsStatus;
    }
    if (params.value.statisticsStatus || params.value.submitStatus) {
      await handleSearch();
    } else {
      Message.info('该分类暂不支持查看');
    }
  };

  const handleTotalSelect = async () => {
    activeEvaluateCard.value = 'total';
    params.value.submitStatus = null;
    params.value.statisticsStatus = null;
    params.value.page = 1;
    params.value.pageSize = 20;
    await handleSearch();
  };

  const handleNotStartedSelect = () => {
    activeEvaluateCard.value = 'notStarted';
    Message.info('未评估状态暂不支持查看详情');
  };
  // 文档模式送教不可见
  const viewAble = computed(() => {
    return (
      currentCard.value !== 'send' ||
      (currentCard.value === 'send' && data.value?.send?.sendVersion === sendVersion.simple)
    );
  });

  const currentSendRecord = ref();
  const currentRecordItem = ref();
  const activeNav = ref(0);
  const loadSendRecord = async () => {
    const { data: res } = await request(
      `/resourceRoom/sendEducationRecord/findBySendEducation/${currentRecord.value.id}`,
      {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      },
    );
    currentSendRecord.value = res.items;
    currentRecordItem.value = currentSendRecord.value?.[0];
  };

  onMounted(async () => {
    if (props.type === 'evaluate') currentCard.value = 'evaluate';
    await loadSchoolCourse();
    await handleSearch();
    if (props.type === 'evaluate') {
      schema.value = await SchemaHelper.getInstanceByDs('/evaluation/customCriterionResult');
    } else if (props.type === 'Plan') {
      schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/individualizedEducation');
      rawSchema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/sendEducationRecord');
    }
    isReady.value = true;
  });
</script>

<template>
  <div class="w-full p-4">
    <!--filter box-->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 p-4 bg-gray-50 rounded shadow mb-6">
      <a-select
        v-if="filters.includes('school')"
        v-model="params.school"
        :size="size"
        placeholder="所属学校"
        :options="branchList"
        :field-names="{ label: 'name', value: 'id' }"
        allow-search
        @change="handleBorOrPeriodChange"
      />
      <a-select
        v-if="filters.includes('period')"
        v-model="params.period"
        :size="size"
        placeholder="学期"
        :options="periodsList"
        allow-search
        @change="handleBorOrPeriodChange"
      />

      <a-select
        v-if="filters.includes('grade')"
        v-model="params.grade"
        allow-search
        :size="size"
        :options="gradeList"
        placeholder="任教年级"
      />
      <a-select
        v-if="filters.includes('gradeClass')"
        v-model="params.gradeClass"
        allow-search
        :size="size"
        :options="gradeClassList"
        placeholder="任教班级"
      />

      <a-select
        v-if="filters.includes('subject')"
        v-model="params.subject"
        :size="size"
        placeholder="科目"
        allow-search
        :options="subjectNames"
        @change="handleSubjectChange($event, 'customCriterionId')"
      />
      <a-select
        v-if="filters.includes('times')"
        v-model="params.times"
        :size="size"
        placeholder="次数"
        :options="times"
        allow-search
        @change="handleTimesChange($event, 'times')"
      />
      <a-select
        v-if="filters.includes('type')"
        v-model="params.type"
        :size="size"
        placeholder="类型"
        allow-search
        :options="genre"
      />
      <div>
        <a-button :size="size" type="outline" @click="handleSearch">
          <template #icon>
            <icon-search />
          </template>
          查询
        </a-button>
        <a-button :size="size" type="text" @click="handleReset">
          <template #icon>
            <icon-refresh />
          </template>
          重置
        </a-button>
      </div>
    </div>

    <!--statistics box-->
    <div class="w-full rounded overflow-hidden mb-6">
      <!--evaluation statistics for evaluate type only-->
      <div v-if="type === 'evaluate'" class="bg-white rounded-lg shadow border p-6">
        <div class="mb-4">
          <h3 class="text-lg font-semibold text-gray-800">评估概况</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <!-- 总评估 -->
          <div 
            class="bg-blue-50 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow border-2"
            :class="activeEvaluateCard === 'total' ? 'border-blue-400' : 'border-transparent'"
            @click="handleTotalSelect()"
          >
            <div class="flex items-center">
              <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <IconUser class="text-xl text-blue-600" />
              </div>
              <div>
                <div class="text-3xl font-bold text-blue-600">
                  {{ data?.overview?.total || 0 }}
                </div>
                <div class="text-sm text-gray-600">总评估</div>
              </div>
            </div>
          </div>
          
          <!-- 已完成 -->
          <div 
            class="bg-green-50 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow border-2"
            :class="activeEvaluateCard === 'finished' ? 'border-green-400' : 'border-transparent'"
            @click="handleSelect(null, { statisticsStatus: 'Enable' })"
          >
            <div class="flex items-center">
              <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                <IconCheckCircle class="text-xl text-green-600" />
              </div>
              <div>
                <div class="text-3xl font-bold text-green-600">
                  {{ data?.overview?.finished || 0 }}
                </div>
                <div class="text-sm text-gray-600">已完成</div>
              </div>
            </div>
          </div>
          
          <!-- 进行中 -->
          <div 
            class="bg-amber-50 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow border-2"
            :class="activeEvaluateCard === 'inProgress' ? 'border-amber-400' : 'border-transparent'"
            @click="handleSelect(null, { submitStatus: 'Draft' })"
          >
            <div class="flex items-center">
              <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mr-4">
                <IconClockCircle class="text-xl text-amber-600" />
              </div>
              <div>
                <div class="text-3xl font-bold text-amber-600">
                  {{ data?.overview?.inProgress || 0 }}
                </div>
                <div class="text-sm text-gray-600">进行中</div>
              </div>
            </div>
          </div>
          
          <!-- 未评估 -->
          <div 
            class="bg-red-50 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow border-2"
            :class="activeEvaluateCard === 'notStarted' ? 'border-red-400' : 'border-transparent'"
            @click="handleNotStartedSelect()"
          >
            <div class="flex items-center">
              <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                <IconExclamationCircle class="text-xl text-red-600" />
              </div>
              <div>
                <div class="text-3xl font-bold text-red-600">
                  {{ data?.overview?.notStarted || 0 }}
                </div>
                <div class="text-sm text-gray-600">未评估</div>
              </div>
            </div>
          </div>
          
          <!-- 已归档 -->
          <div 
            class="bg-slate-50 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow border-2"
            :class="activeEvaluateCard === 'archived' ? 'border-blue-400' : 'border-transparent'"
            @click="handleSelect(null, { submitStatus: 'Submitted' })"
          >
            <div class="flex items-center">
              <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <IconArchive class="text-xl text-blue-600" />
              </div>
              <div>
                <div class="text-3xl font-bold text-blue-600">
                  {{ data?.overview?.archived || 0 }}
                </div>
                <div class="text-sm text-gray-600">已归档</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!--plan statistics for other types-->
      <div v-else class="w-full h-full">
        <div class="flex flex-wrap gap-4 mb-6">
          <div
            v-for="(item, index) in modules[type]"
            :key="index"
            class="bg-white cursor-pointer border rounded-lg shadow flex-1 px-4 pt-4 pb-2"
            :class="currentIndex === index ? ' border-blue-400' : ''"
            @click="handleSelectCard(index, item)"
          >
            <div class="flex justify-between">
              <span class="text-gray-500">{{ item.title }}</span>
              <component
                :is="IconSearch"
                v-if="false"
                class="w-[30px] h-[30px] bg-gray-50 rounded-full items-center p-1"
              />
            </div>
            <div class="mt-2 flex justify-start items-center space-x-4">
              <div v-for="(children, idx) in item.children" :key="idx" class="flex items-center space-x-2">
                <div class="flex flex-col items-center justify-center">
                  <div
                    class="text-2xl font-bold duration-100 transform-origin-center hover:scale-110"
                    :class="children?.style"
                    @click="handleSelect(item, children)"
                  >
                    {{ data?.[item.key]?.[children.key] || 0 }}
                  </div>
                  <div class="text-xs mt-1 text-gray-500">
                    {{ children.label }}
                  </div>
                </div>
                <div v-if="idx != item.children.length - 1" class="text-gray-300">|</div>
              </div>
            </div>
            <a-progress
              v-if="item?.progress"
              class="mt-4"
              :stroke-width="5"
              :percent="data?.[item.key]?.['progress'] || 0"
              :color="{
                '0%': 'rgb(255,193,73)',
                '100%': 'rgb(255,129,75)',
              }"
            />
          </div>
        </div>
      </div>
    </div>
    <a-table
      v-if="props.type !== 'implement'"
      :data="filterList || []"
      :columns="columns?.[currentCard] || []"
      :bordered="{ cell: true }"
      :pagination="pagination"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    >
      <template #num="{ rowIndex }">
        {{ rowIndex + 1 }}
      </template>
      <template #submitStatus="{ record }">
        <span>{{ record.submitStatus === 'Draft' ? '草稿' : '已提交' }}</span>
      </template>
      <template #type="{ record }">
        <span>{{ record.type === 'Criterion' ? '量表' : '诊断' }}</span>
      </template>
      <template #guardianConfirmed="{ record }">
        <a-tag v-if="record.guardianConfirmed" size="mini" color="green">是</a-tag>
        <a-tag v-else size="mini" color="red">否</a-tag>
      </template>
      <template #assessmentTeachers="{ record }">
        <UserAvatarListDisplay v-if="isReady" :raw="record.collaborators"></UserAvatarListDisplay>
      </template>
      <template #operate="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button size="mini" @click="handleView(record)">查看</a-button>
          <a-popover content="驳回后需老师再次提交才纳入统计">
            <a-button
              v-if="['iep'].includes(currentCard)"
              size="mini"
              type="outline"
              status="danger"
              @click="handleReject(record)"
            >
              <template #icon> <icon-close /> </template>
              驳回提交
            </a-button>
          </a-popover>
        </div>
      </template>
    </a-table>
  </div>
  <a-modal v-if="viewAble" v-model:visible="viewVisible" fullscreen title="详情" :body-style="bodyStyle">
    <record-detail
      v-if="viewVisible && type !== 'evaluate' && currentCard !== 'send'"
      ref="recordDetailRef"
      :raw="currentRecord"
      :schema="schema"
    >
      <template #default> </template>
    </record-detail>
    <analysis
      v-if="viewVisible && type === 'evaluate'"
      v-model:visible="viewVisible"
      :result-id="currentRecord?.id"
      :current-times="currentRecord?.times"
    />
    <a-tabs
      v-if="currentCard === 'send' && viewAble && viewVisible"
      default-active-key="sendPlan"
      @change="loadSendRecord"
    >
      <a-tab-pane key="sendPlan" title="送教计划">
        <record-detail :raw="currentRecord" :schema="schema">
          <template #default> </template>
        </record-detail>
      </a-tab-pane>
      <a-tab-pane key="sendRecord" title="送教记录" class="bg-gray-50">
        <div class="flex h-screen">
          <div class="w-auto bg-white p-4 border border-gray-200 rounded mr-2 shadow">
            <div class="mb-2">
              <span class="font-bold mr-2">{{ currentRecord?.student?.name }}</span>
              <a-tag size="small" :color="randomColor(33)">{{ currentRecord?.period }}</a-tag>
            </div>
            <a-divider />
            <ul class="max-h-[90vh] overflow-y-auto">
              <li
                v-for="(i, index) in currentSendRecord"
                :key="i.id"
                class="cursor-pointer mb-2 bg-gray-100 hover:shadow-lg px-2 py-1 rounded shadow"
                :class="activeNav == index ? 'bg-stone-400 text-white' : ''"
                @click="
                  () => {
                    currentRecordItem = i;
                    activeNav = index;
                  }
                "
              >
                <div>
                  <div>{{ `第${index + 1}次记录` }}</div>
                  <div class="text-xs text-gray-400" :class="activeNav == index ? ' text-white' : ''">
                    {{ i?.date }}
                  </div>
                </div>
              </li>
            </ul>
          </div>

          <div class="flex-1 bg-white p-4 overflow-y-auto border rounded shadow">
            <record-detail :raw="currentRecordItem" :schema="rawSchema" />
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
  <detail
    v-else-if="viewVisible && !viewAble"
    v-model:visible="viewVisible"
    :record="currentRecord"
    @update:model-value="viewVisible = false"
  />

  <supervisionResultListModal v-if="resultListVisible" v-model:visible="resultListVisible" />
</template>

<style scoped lang="scss"></style>
