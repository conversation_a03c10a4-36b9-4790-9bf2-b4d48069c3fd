<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getAdminClientNature } from '@repo/components/utils/utils';

  // 浏览器缓存管理
  const CACHE_KEYS = {
    GRADES: 'applicable_scope_grades_cache',
    SUBGROUPS: 'applicable_scope_subgroups_cache',
    GRADE_TIMESTAMP: 'applicable_scope_grades_timestamp',
    SUBGROUP_TIMESTAMP: 'applicable_scope_subgroups_timestamp',
  };

  const CACHE_DURATION = 30 * 60 * 1000; // 30分钟缓存

  // 获取缓存数据
  const getCachedData = (cacheKey: string, timestampKey: string): Map<number, string> | null => {
    try {
      const timestamp = sessionStorage.getItem(timestampKey);
      const now = Date.now();

      if (!timestamp || now - parseInt(timestamp, 10) > CACHE_DURATION) {
        // 缓存过期，清除
        sessionStorage.removeItem(cacheKey);
        sessionStorage.removeItem(timestampKey);
        return null;
      }

      const cached = sessionStorage.getItem(cacheKey);
      if (cached) {
        const data = JSON.parse(cached);
        return new Map(Object.entries(data).map(([k, v]) => [parseInt(k, 10), v as string]));
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('读取缓存失败:', error);
    }
    return null;
  };

  // 设置缓存数据
  const setCachedData = (cacheKey: string, timestampKey: string, data: Map<number, string>) => {
    try {
      const obj = Object.fromEntries(data.entries());
      sessionStorage.setItem(cacheKey, JSON.stringify(obj));
      sessionStorage.setItem(timestampKey, Date.now().toString());
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('设置缓存失败:', error);
    }
  };

  // 全局Promise管理（仅在当前页面生命周期内有效）
  let gradeLoadingPromise: Promise<Map<number, string>> | null = null;
  let subgroupLoadingPromise: Promise<Map<number, string>> | null = null;

  interface ApplicableScopeData {
    scopeType: 'grade' | 'subgroup';
    targetIds: number[];
  }

  interface NameItem {
    id: number;
    name: string;
  }

  const props = defineProps({
    value: {
      type: Object as () => ApplicableScopeData | null,
      default: null,
    },
    record: {
      type: Object,
      default: () => ({}),
    },
  });

  // 状态管理
  const loading = ref(false);
  const nameMap = ref<Map<number, string>>(new Map());

  // 获取当前机构性质
  const orgNature = getAdminClientNature();

  // 计算属性
  const isAllApplicable = computed(() => !props.value || !props.value.targetIds?.length);

  const scopeTypeLabel = computed(() => {
    if (isAllApplicable.value) return '';
    return props.value?.scopeType === 'grade' ? '年级' : '分组';
  });

  const targetNames = computed(() => {
    if (isAllApplicable.value) return [];

    return (props.value?.targetIds || []).map((id) => nameMap.value.get(id)).filter(Boolean) as string[];
  });

  const displayText = computed(() => {
    if (isAllApplicable.value) {
      return '全部适用';
    }

    if (targetNames.value.length === 0) {
      return loading.value ? '加载中...' : '数据异常';
    }

    return targetNames.value.join('、');
  });

  const tagColor = computed(() => {
    if (isAllApplicable.value) return 'green';
    return props.value?.scopeType === 'grade' ? 'blue' : 'orange';
  });

  // 加载全部年级数据
  const loadAllGrades = async (): Promise<Map<number, string>> => {
    // 先检查缓存
    const cached = getCachedData(CACHE_KEYS.GRADES, CACHE_KEYS.GRADE_TIMESTAMP);
    if (cached && cached.size > 0) {
      return cached;
    }

    // 复用正在进行的请求
    if (gradeLoadingPromise) {
      return gradeLoadingPromise;
    }

    gradeLoadingPromise = (async () => {
      try {
        const { data } = await request('/teacher/schoolGrade', {
          method: 'GET',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            pageSize: 999,
            orgNature,
          },
        });

        const grades = data.items || [];
        const gradeMap = new Map<number, string>();
        grades.forEach((grade: NameItem) => {
          gradeMap.set(grade.id, grade.name);
        });

        // 缓存数据
        setCachedData(CACHE_KEYS.GRADES, CACHE_KEYS.GRADE_TIMESTAMP, gradeMap);
        return gradeMap;
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('加载年级名称失败:', error);
        return new Map<number, string>();
      } finally {
        gradeLoadingPromise = null;
      }
    })();

    return gradeLoadingPromise;
  };

  // 加载全部分组数据
  const loadAllSubgroups = async (): Promise<Map<number, string>> => {
    // 先检查缓存
    const cached = getCachedData(CACHE_KEYS.SUBGROUPS, CACHE_KEYS.SUBGROUP_TIMESTAMP);
    if (cached && cached.size > 0) {
      return cached;
    }

    // 复用正在进行的请求
    if (subgroupLoadingPromise) {
      return subgroupLoadingPromise;
    }

    subgroupLoadingPromise = (async () => {
      try {
        const { data } = await request('/resourceRoom/subgroup', {
          method: 'GET',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            pageSize: 999,
            orgNature,
          },
        });

        const subgroups = data.items || [];
        const subgroupMap = new Map<number, string>();
        subgroups.forEach((subgroup: NameItem) => {
          subgroupMap.set(subgroup.id, subgroup.name);
        });

        // 缓存数据
        setCachedData(CACHE_KEYS.SUBGROUPS, CACHE_KEYS.SUBGROUP_TIMESTAMP, subgroupMap);
        return subgroupMap;
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('加载分组名称失败:', error);
        return new Map<number, string>();
      } finally {
        subgroupLoadingPromise = null;
      }
    })();

    return subgroupLoadingPromise;
  };

  // 统一的名称加载函数
  const loadNamesForIds = async (ids: number[], type: 'grade' | 'subgroup') => {
    try {
      loading.value = true;

      // 获取数据（从缓存或API）
      const dataMap = type === 'grade' ? await loadAllGrades() : await loadAllSubgroups();

      // 从缓存中获取所需数据
      ids.forEach((id) => {
        const name = dataMap.get(id);
        if (name) {
          nameMap.value.set(id, name);
        }
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`加载${type === 'grade' ? '年级' : '分组'}名称失败:`, error);
    } finally {
      loading.value = false;
    }
  };

  // 加载名称数据
  const loadNames = async () => {
    if (isAllApplicable.value) return;

    const targetIds = props.value?.targetIds || [];
    if (targetIds.length === 0) return;

    // 检查本地组件缓存
    const uncachedIds = targetIds.filter((id) => !nameMap.value.has(id));
    if (uncachedIds.length === 0) return;

    await loadNamesForIds(targetIds, props.value!.scopeType);
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadNames();
  });

  // 监听props.value变化，重新加载名称
  watch(
    () => props.value,
    (newValue, oldValue) => {
      if (newValue !== oldValue) {
        loadNames();
      }
    },
    { deep: true, immediate: false },
  );
</script>

<template>
  <div class="applicable-scope-view">
    <!-- 简化显示模式：用于列表 -->
    <div v-if="$attrs.simple">
      <div v-if="isAllApplicable">
        <a-tag :color="tagColor" size="small">
          {{ displayText }}
        </a-tag>
      </div>
      <div v-else class="flex flex-wrap gap-1 items-start">
        <a-tag
          v-for="name in targetNames"
          :key="name"
          :color="tagColor"
          size="small"
          class="whitespace-nowrap m-0"
        >
          {{ name }}
        </a-tag>
      </div>
    </div>
    
    <!-- 详细显示模式：用于详情页 -->
    <div v-else>
      <div class="flex items-center mb-2">
        <span class="font-medium mr-2 text-gray-800">适用范围：</span>
        <a-tag :color="tagColor" size="medium">
          {{ displayText }}
        </a-tag>
      </div>

      <!-- 显示具体的目标名称 -->
      <div v-if="!isAllApplicable && targetNames.length > 0" class="mt-3">
        <div class="mb-2">
          <span class="text-sm text-gray-600 font-medium">{{ scopeTypeLabel }}列表：</span>
        </div>
        <div class="flex flex-wrap gap-1">
          <a-tag v-for="name in targetNames" :key="name" :color="tagColor" size="small">
            {{ name }}
          </a-tag>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center gap-2 text-gray-500 text-xs mt-2">
        <a-spin size="mini" />
        <span>加载名称中...</span>
      </div>
    </div>
  </div>
</template>
