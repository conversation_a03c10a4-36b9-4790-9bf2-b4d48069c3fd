<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import { getAdminClientNature } from '@repo/components/utils/utils';
import { Message } from '@arco-design/web-vue';

interface ApplicableScopeData {
  scopeType: 'grade' | 'subgroup';
  targetIds: number[];
}

interface OptionItem {
  id: number;
  name: string;
}

const props = defineProps({
  modelValue: {
    type: Object as () => ApplicableScopeData | null,
    default: null,
  },
  record: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits<{
  'update:modelValue': [value: ApplicableScopeData | null];
}>();

// 状态管理
const loading = ref(false);
const grades = ref<OptionItem[]>([]);
const subgroups = ref<OptionItem[]>([]);
const isAllApplicable = ref(false);
const selectedTargetIds = ref<number[]>([]);

// 获取当前机构性质
const orgNature = getAdminClientNature();
const isSpecialSchool = orgNature === '特殊教育学校';

// 计算属性
const scopeType = computed(() => (isSpecialSchool ? 'grade' : 'subgroup'));
const options = computed(() => (isSpecialSchool ? grades.value : subgroups.value));
const placeholder = computed(() => (isSpecialSchool ? '请选择适用年级' : '请选择适用分组'));
const label = computed(() => (isSpecialSchool ? '适用年级' : '适用分组'));

// 双向绑定处理
const internalValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// 更新模型值
const updateModelValue = () => {
  if (isAllApplicable.value || selectedTargetIds.value.length === 0) {
    internalValue.value = null;
  } else {
    internalValue.value = {
      scopeType: scopeType.value,
      targetIds: [...selectedTargetIds.value],
    };
  }
};

// 加载年级数据
const loadGrades = async () => {
  try {
    loading.value = true;
    const { data } = await request('/teacher/schoolGrade', {
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
        orgNature,
      },
    });
    grades.value = data.items || [];
  } catch (error) {
    Message.error('加载年级数据失败');
  } finally {
    loading.value = false;
  }
};

// 加载分组数据
const loadSubgroups = async () => {
  try {
    loading.value = true;
    const { data } = await request('/resourceRoom/subgroup', {
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
        orgNature,
      },
    });
    subgroups.value = data.items || [];
  } catch (error) {
    Message.error('加载分组数据失败');
  } finally {
    loading.value = false;
  }
};

// 处理"全部适用"切换
const handleAllApplicableChange = (checked: boolean) => {
  if (checked) {
    selectedTargetIds.value = [];
    internalValue.value = null;
  } else {
    updateModelValue();
  }
};

// 处理目标选择变化
const handleTargetChange = (targetIds: number[]) => {
  selectedTargetIds.value = targetIds;
  if (targetIds.length === 0) {
    isAllApplicable.value = true;
    internalValue.value = null;
  } else {
    isAllApplicable.value = false;
    updateModelValue();
  }
};

// 初始化组件状态
const initializeComponent = () => {
  if (props.modelValue) {
    isAllApplicable.value = false;
    selectedTargetIds.value = [...(props.modelValue.targetIds || [])];
  } else {
    isAllApplicable.value = true;
    selectedTargetIds.value = [];
  }
};

// 监听modelValue变化
watch(
  () => props.modelValue,
  () => {
    initializeComponent();
  },
  { immediate: true },
);

// 组件挂载时加载数据
onMounted(async () => {
  if (isSpecialSchool) {
    await loadGrades();
  } else {
    await loadSubgroups();
  }
  initializeComponent();
});
</script>

<template>
  <div class="applicable-scope-input">
    <div class="scope-header">
      <span class="label">{{ label }}：</span>
      <a-checkbox v-model="isAllApplicable" @change="handleAllApplicableChange"> 全部适用 </a-checkbox>
    </div>

    <div v-if="!isAllApplicable" class="scope-selector">
      <a-select
        :model-value="selectedTargetIds"
        :placeholder="placeholder"
        :loading="loading"
        multiple
        allow-clear
        :max-tag-count="3"
        @update:model-value="handleTargetChange"
      >
        <a-option v-for="item in options" :key="item.id" :value="item.id" :label="item.name">
          {{ item.name }}
        </a-option>
      </a-select>

      <div v-if="selectedTargetIds.length === 0 && !isAllApplicable" class="error-tip">
        请至少选择一个{{ isSpecialSchool ? '年级' : '分组' }}
      </div>
    </div>

    <div v-if="isAllApplicable" class="all-applicable-tip">
      当前设置为全部适用，该量表将对所有{{ isSpecialSchool ? '年级' : '分组' }}生效
    </div>
  </div>
</template>

<style scoped lang="scss">
.applicable-scope-input {
  .scope-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .label {
      font-weight: 500;
      margin-right: 12px;
    }
  }

  .scope-selector {
    .error-tip {
      color: #f53f3f;
      font-size: 12px;
      margin-top: 4px;
    }
  }

  .all-applicable-tip {
    color: #86909c;
    font-size: 12px;
    margin-top: 4px;
    padding: 8px 12px;
    background-color: #f7f8fa;
    border-radius: 4px;
  }
}
</style>