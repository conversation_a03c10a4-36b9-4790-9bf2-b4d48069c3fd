<script lang="ts" setup>
  import AiDialogBox from '@repo/components/common/aiDialogBox.vue';
  import { computed, onMounted, ref, watch } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import { getToken } from '@repo/infrastructure/auth';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['update:visible']);
  const visible = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });
  const dialogRef = ref(null);
  const handleUpload = (val: any) => {
    if (dialogRef.value) dialogRef.value.setFileList(val);
  };

  const sessionId = ref();

  const chatResponse = ref('');

  const requestStreamChat = async (msg: string) => {
    chatResponse.value = '';
    const requestUrl = new URL(`${PROJECT_URLS.MAIN_PROJECT_API}/ai/assessment/streamCall`);
    requestUrl.searchParams.append('message', msg);

    const fileList = dialogRef.value?.getFileList() ?? [];

    if (sessionId.value && sessionId.value !== '') requestUrl.searchParams.append('sessionId', sessionId.value);

    const response = await fetch(requestUrl.toString(), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Authorization': `Bearer ${getToken()}`,
        'LoginSource': 'PC',
        'Accept-Language': 'zh-CN',
        'X-User-Type': 'System',
        'Request-Client-Role': 'Company',
      },
      body: JSON.stringify(
        fileList.map((item) => ({
          id: item.id,
          name: item.name,
          udf1: item.url,
        })),
      ),
    });

    const reader = response.body?.getReader();
    const decoder = new TextDecoder('utf-8');

    let partial = '';
    while (true) {
      // eslint-disable-next-line no-await-in-loop
      const { value, done } = await reader.read();
      if (done) {
        dialogRef.value?.saveChatHistory();
        dialogRef.value?.reset();
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      partial += chunk;

      const lines = partial.split('\n');

      partial = lines.pop() || '';

      // eslint-disable-next-line no-restricted-syntax
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const jsonText = line.slice(5).trim();
          try {
            const json = JSON.parse(jsonText);
            const text = json.output?.text || '';
            sessionId.value = json.output?.sessionId || '';
            chatResponse.value += text;
          } catch (err) {
            console.error('⚠️ JSON 解析失败：', jsonText);
          }
        }
      }
    }
  };
  onMounted(() => {
    sessionId.value = localStorage.getItem(`behavior_chat_sessionId`) || '';
    dialogRef.value?.load();
  });
  watch(
    () => sessionId.value,
    (newVal) => {
      if (newVal) {
        localStorage.setItem(`behavior_chat_sessionId`, newVal);
        dialogRef.value?.load();
      }
    },
    { deep: true },
  );
</script>

<template>
  <ai-dialog-box
    ref="dialogRef"
    v-model:visible="visible"
    :response="chatResponse"
    :session-id="sessionId"
    @send="requestStreamChat"
  >
    <!--handleSend-->
    <!--requestStreamChat-->
    <template #boxTitle>
      <span class="font-bold">AI助手{{ sessionId }}</span>
    </template>
    <template #toolBar>
      <div
        :class="[
          'flex justify-end items-center  mr-6 bg-white rounded-full px-6 py-2 space-x-2',
          'border border-dashed border-black/20',
          'hover:cursor-pointer hover:border-blue-300 ',
        ]"
      >
        <!--模型支持的文件-->
        <div class="flex justify-center items-center">
          <uploader
            sub-folder=""
            :limit="10"
            :show-file-list="false"
            :max-size="4 * 1024"
            accept=".txt,.docx,.pdf,.xlsx,.epub,.mobi,.md,.csv,.json,.bmp,.png,.jpg,.jpeg,.gif"
            @uploaded="handleUpload"
          >
            <template #uploadButton>
              <div class="flex justify-center items-center max-w-[25px]">
                <icon-attachment size="25" class="hover:text-cyan-500" />
              </div>
            </template>
          </uploader>
        </div>
        <a-popover>
          <icon-tool size="25" class="hover:text-cyan-500" />
          <template #title>
            <div>
              <span class="text-sm font-bold">工具箱</span>
            </div>
          </template>
          <template #content>
            <div class="w-40 h-40"></div>
          </template>
        </a-popover>
      </div>
    </template>
  </ai-dialog-box>
</template>

<style scoped lang="scss"></style>
