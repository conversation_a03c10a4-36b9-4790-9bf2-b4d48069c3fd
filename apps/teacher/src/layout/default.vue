<template>
  <div v-if="ready" class="p-2">
    <a-layout class="gap-2">
      <a-layout-sider collapsible breakpoint="xl" class="layout-side">
        <a-menu
          v-model:default-selected-keys="defaultSelectKeys"
          v-model:default-open-keys="defaultExpandKeys"
          :accordion="true"
        >
          <div class="user-info flex gap-2">
            <avatar-display :size="40" :user-info="userStore" />
            <div class="text-sm flex flex-col username gap-1 flex-1">
              <div class="flex gap-2 justify-between">
                {{ userInfo.name }}
              </div>
              <div class="flex gap-2 mt">
                <small
                  class="cursor-pointer text-blue-500 flex items-center"
                  @click="() => router.push('/teacher/profile')"
                >
                  <IconUser />
                  个人中心
                </small>
                <a-popconfirm content="确认要退出登陆吗？" @ok="handleLogout">
                  <small class="cursor-pointer text-blue-500 flex items-center"> <IconExport />退出 </small>
                </a-popconfirm>
              </div>
            </div>
          </div>
          <a-divider />
          <a-menu-item key="dashboard" @click="() => router.push('/')">
            <template #icon>
              <IconHome />
            </template>
            工作台
          </a-menu-item>
          <main-menu :menus="menus" />
        </a-menu>
        <template #trigger="{ collapsed }">
          <a-tooltip v-if="collapsed" content="展开侧边栏">
            <IconCaretRight />
          </a-tooltip>
          <div v-else>
            <IconCaretLeft />
            收起侧边栏
          </div>
        </template>
      </a-layout-sider>
      <a-layout class="flex-col gap-2">
        <a-card class="header">
          <div class="flex justify-between items-center">
            <breadcrumb />
            <div v-if="env !== 'production'" class="red">{{ env }}</div>
            <nav-right />
          </div>
        </a-card>
        <div class="w-full page-content-wrapper flex-1 relative">
          <span class="fixed bottom-20 right-5 z-50 overflow-hidden rounded-lg">
            <ai-button class="!m-0" tooltip="AI 助手" @ok="dialogVisible = true" />
          </span>
          <router-view />
        </div>
      </a-layout>
    </a-layout>
    <browser-check />
    <aiChatModal v-model:visible="dialogVisible" />
  </div>
</template>

<script lang="ts" setup>
  import BrowserCheck from '@/common/components/browserCheck.vue';
  import NavRight from '@/layout/components/navRight.vue';
  import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
  import MainMenu from '@/layout/components/mainMenu.vue';
  import { useRoute, useRouter } from 'vue-router';
  import Breadcrumb from '@/layout/components/breadcrumb.vue';
  import { AvatarDisplay } from '@repo/ui/components/data-display/components';
  import { ENV } from '@repo/env-config';
  import { nextTick, onMounted, ref } from 'vue';
  import useMenuRenameStore from '@repo/infrastructure/store/menuRenameStore';
  import AiButton from '@repo/components/common/aiButton.vue';
  import AiChatModal from '@/layout/components/aiChatModal.vue';

  const env = ENV.MODE?.toUpperCase();
  const menuRenameStore = useMenuRenameStore();

  const menuStore = useUserMenuStore();
  let menus;
  const userStore = useUserStore();
  const { userInfo } = userStore;
  const ready = ref(false);
  const router = useRouter();
  const route = useRoute();
  const defaultSelectKeys = ref<string[]>([]);
  const defaultExpandKeys = ref<string[]>([]);
  const dialogVisible = ref(false);

  const handleLogout = () => {
    userStore.logout();
  };

  onMounted(async () => {
    await menuRenameStore.loadData();

    await nextTick(() => {
      menus = menuStore.getUserMenus('teacher');
      const menuInfo = menuStore.getCurrentTeacherMenuInfo(route);
      if (menuInfo.module) {
        if (menuInfo.subModules?.length) {
          let children = menuInfo.module.children || [];
          // eslint-disable-next-line no-restricted-syntax
          for (const subModule of menuInfo.subModules) {
            const child = children.find((c) => c.key === subModule);
            if (child?.children?.length) {
              children = child.children || [];
            } else {
              defaultSelectKeys.value = [child?.permission];
            }
          }
        } else {
          defaultExpandKeys.value = [menuInfo.app.permission];
          defaultSelectKeys.value = [menuInfo.app.permission, menuInfo.module.permission];
        }
      } else if (menuInfo.app) {
        defaultSelectKeys.value = [menuInfo.app?.permission];
        defaultExpandKeys.value = [menuInfo.app.permission];
      } else {
        defaultSelectKeys.value = ['dashboard'];
      }

      ready.value = true;
    });
  });
</script>

<style lang="scss" scoped>
  .header :deep .arco-card-body {
    padding: 8px;
  }

  .user-info {
    padding: 16px 10px 0;
    transition: all linear 0.2s;
    :deep .arco-avatar {
      transition: all linear 0.2s;
    }
  }

  .arco-menu-collapsed {
    .user-info {
      padding: 8px 0 0;
      :deep .arco-avatar {
        transform: scale(0.9);
      }
      .username {
        display: none;
      }
    }
  }

  .layout-side {
    //max-height: calc(100vh - 1em);
  }

  .page-content-wrapper {
    :deep {
      .arco-card {
        max-width: 100%;
      }
      & > div,
      .main-wrap-content {
        min-height: calc(100vh - 1.5em - 42px);
      }
    }
  }

  :deep {
    .arco-card-size-small .arco-card-header {
      height: auto;
      min-height: 40px;
    }

    .arco-card-size-medium .arco-card-header {
      height: auto;
      min-height: 46px;
    }
  }
</style>
