<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { computed, onMounted, PropType, provide, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useUserStore } from '@repo/infrastructure/store';
  import CustomizeComponent from '@repo/infrastructure/customizeComponent/customizeComponent.vue';
  import inputTable from '@repo/ui/components/customizeComponent/components/inputTable.vue';
  import { CrudForm } from '@repo/ui/components';
  import Uploader from '@repo/ui/components/upload/uploader.vue';

  import { merge, cloneDeep } from 'lodash';
  import { debouncedWatch } from '@vueuse/core';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';

  provide('merge', merge);
  provide('cloneDeep', cloneDeep);
  provide('debouncedWatch', debouncedWatch);
  provide('request', request);
  provide('PROJECT_URLS', PROJECT_URLS);
  provide('uploader', Uploader);

  const props = defineProps({
    plan: {
      type: Object as PropType<any>,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  });
  const schema = ref(null);
  const emit = defineEmits(['update:visible']);
  provide('inputTable', inputTable);

  const queryParams = computed(() => {
    return {
      sort: '-id',
      sendEducationPlan: props.plan.id,
    };
  });
  const dateRange = computed(() => {
    return props.plan.dateRange?.map((item) => new Date(item));
  });

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  const { userInfo } = useUserStore();

  const defaultEditData = {
    school: userInfo?.branchOffice?.name,
    teacher: userInfo?.name,
    effectEvaluation: props.plan.planDetails?.map((item) => {
      return {
        target: item.target,
      };
    }),
    sendEducationPlan: props.plan,
  };

  onMounted(async () => {
    const rawSchema = await SchemaHelper.getInstanceByDs('/resourceRoom/sendEducationRecord');

    rawSchema.schemaFields = rawSchema.schemaFields.map((field) => {
      if (field.key === 'date') {
        field.inputWidgetProps = {
          ...field.inputWidgetProps,
          disabledDate: (current) => {
            return current && (current < dateRange.value?.[0] || current > dateRange.value?.[1]);
          },
        };
      }
      return field;
    });

    schema.value = rawSchema;
  });

  const crudFormRef = ref<any>(null);
  const tableRef = ref<any>(null);
  const handlePreOk = async () => {
    // await crudFormRef.value?.handleSubmit();
  };

  const handleEditFormOk = async (editData) => {
    console.log('editData', editData);
    // here choose to update or create
  };

  const customizeRef = ref(null);
  const detailVisible = ref(false);
  const currentSendRecord = ref(null);
  const handleRowAction = (action, row) => {
    currentSendRecord.value = row;
    if (action.key === 'viewDetail') {
      detailVisible.value = true;
    }
  };
  const handleSubmit = async () => {
    await tableRef.value.loadData();
  };
  const visibleColumns = ref(['type', 'date', 'school', 'teacher', 'classHour', 'personInCharge', 'createdDate']);
  const crudFormCallback = () => {};
</script>

<template>
  <a-modal v-model:visible="modalVisible" fullscreen :on-before-ok="handlePreOk" hide-cancel :render-to-body="false">
    <template #title> {{ plan.student?.name }} {{ plan.period }} 送教记录</template>
    <table-with-modal-form
      v-if="schema"
      ref="tableRef"
      :form-content-ref="customizeRef?.isDefaultComponent ? crudFormRef : customizeRef"
      module-name="送教记录"
      :schema="schema"
      :modal-form-property="{ fullscreen: false }"
      :default-edit-value="defaultEditData"
      :default-query-params="queryParams"
      :visible-columns="visibleColumns"
      :modal-width="'70%'"
      @form-ok="handleEditFormOk"
      @row-action="handleRowAction"
      @submit="handleSubmit"
    >
      <template #form-content="{ currentRecord, updateRecord }">
        <div class="w-full flex justify-center items-center">
          <customize-component
            v-if="modalVisible"
            ref="customizeRef"
            :model-value="currentRecord"
            :schema="schema"
            :send-education-plan="plan"
            module="SendEducationRecord"
            page="Edit"
            @update:model-value="updateRecord"
          >
            <template #default>
              <crud-form
                v-if="modalVisible"
                ref="crudFormRef"
                :model-value="currentRecord"
                :schema="schema"
                :show-actions="false"
                :callback="crudFormCallback"
                @update:model-value="updateRecord"
              />
            </template>
          </customize-component>
        </div>
        <!--        </customize-component>-->

        <!--        <customize-component-->
        <!--          v-if="modalVisible"-->
        <!--          :model-value="currentRecord"-->
        <!--ref="customizeRef"-->
        <!--          :schema="schema"-->
        <!--:send-education-plan="plan"-->
        <!--          module="SendEducationRecord"-->
        <!--          page="Edit"-->
        <!--          @update:model-value="updateRecord"-->
        <!--        >-->
        <!--          <crud-form-->
        <!--            ref="crudFormRef"-->
        <!--            :model-value="currentRecord"-->
        <!--            :schema="schema"-->
        <!--            :show-actions="false"-->
        <!--            @update:model-value="updateRecord"-->
        <!--          />-->
        <!--        </customize-component>-->
      </template>
    </table-with-modal-form>
    <a-modal v-model:visible="detailVisible" width="auto" title="送教记录">
      <!--<customizedSendRecordViewDemo v-if="detailVisible" :current-record="currentSendRecord" />-->
      <div class="flex justify-center items-center w-full">
        <customize-component
          v-if="detailVisible"
          :current-record="currentSendRecord"
          module="SendEducationRecord"
          page="View"
          model-value=""
        >
          <record-detail :raw="currentSendRecord" :schema="schema" />
        </customize-component>
      </div>
    </a-modal>
  </a-modal>
</template>
