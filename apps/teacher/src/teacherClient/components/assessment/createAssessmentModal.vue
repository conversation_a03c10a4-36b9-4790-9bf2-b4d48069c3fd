<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import StudentSelect from '@repo/components/student/studentSelect.vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useUserStore } from '@repo/infrastructure/store';
  import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
  import { UNIT_NATURES_MAP } from '@repo/infrastructure/constants';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  });

  const router = useRouter();
  const emit = defineEmits(['update:modelValue']);
  const selectedStudents = ref<any[]>([]);
  const periods = getPeriodsList();
  const formData = ref<any>({
    criterionId: '',
    evaluationDate: new Date(),
    period: null,
  });
  const criterionType = ref<string>('Criterion');
  
  // 新增：适用量表列表和加载状态
  const applicableCriteria = ref<any[]>([]);
  const criteriaLoading = ref(false);

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  });

  const handleClose = () => {
    visible.value = false;
  };

  const handleStudentChange = (a, b, student) => {
    if (criterionType.value === 'Diagnosis') {
      if (selectedStudents.value.length > 0) {
        Message.error('诊断评估只能选择一个学生');
        return;
      }
    }
    if (selectedStudents.value.find((item) => item.id === student.id)) {
      Message.error('该学生已在列表中');
      return;
    }
    selectedStudents.value.push(student);
  };

  const handleSelectAll = (studentList: any[]) => {
    const studentIds = selectedStudents.value.map((item) => item.id);
    const notSelectStudents = studentList.filter((s) => !studentIds.includes(s.id)) || [];
    selectedStudents.value = [...selectedStudents.value, ...notSelectStudents];
  };

  const handleCancel = () => {
    selectedStudents.value = [];
  };
  const handleDeleteStudent = (student) => {
    selectedStudents.value = selectedStudents.value.filter((item) => item.id !== student.id);
  };

  const formRef = ref(null);
  const handleGoStart = async () => {
    if (formRef.value) {
      const res = await formRef.value.validate();

      if (res) {
        return false;
      }
    }
    if (!formData.value.criterionId) {
      Message.error('请选择评估量表');
      return false;
    }
    if (selectedStudents.value.length === 0) {
      Message.error('请至少选择一名学生以开始评估');
      return false;
    }
    const url = `/teacher/teaching/assessment/${criterionType.value.toLowerCase()}`;
    router.push({
      path: url,
      query: {
        criterionId: formData.value.criterionId,
        evaluationDate: dayjs(formData.value.evaluationDate).format('YYYY-MM-DD'),
        students: selectedStudents.value.map((item) => item.id).join(),
        period: formData.value.period,
      },
    });

    visible.value = false;
    return true;
  };
  const userStore = useUserStore();
  const orgNature = userStore.getUserNature();

  const studentDefaultQueryParams = ref({});

  const handleOpen = async () => {
    if (orgNature === UNIT_NATURES_MAP.SpecialEduCommittee) {
      studentDefaultQueryParams.value = {
        status: 'WaitingResettlement',
      };
    }
  };

  const draggingIndex = ref<number | null>(null);
  const dropIndex = ref<number | null>(null);

  const handleDragStart = (index: number) => {
    draggingIndex.value = index;
  };

  const handleDragEnter = (index: number) => {
    dropIndex.value = index;
  };

  const handleDragOver = (e: DragEvent, index: number) => {
    e.preventDefault();
    dropIndex.value = index;
  };

  const handleDragEnd = () => {
    if (draggingIndex.value !== null && dropIndex.value !== null && draggingIndex.value !== dropIndex.value) {
      const newList = [...selectedStudents.value];
      const [removed] = newList.splice(draggingIndex.value, 1);
      newList.splice(dropIndex.value, 0, removed);
      selectedStudents.value = newList;
    }
    draggingIndex.value = null;
    dropIndex.value = null;
  };

  // 监听学生列表变化，动态加载适用量表
  watch(
    selectedStudents,
    async (students) => {
      if (students.length === 0) {
        applicableCriteria.value = [];
        formData.value.criterionId = '';
        return;
      }
      await loadApplicableCriteria();
    },
    { deep: true }
  );

  // 监听评估类型变化，重新加载量表
  watch(
    () => criterionType.value,
    async () => {
      formData.value.criterionId = '';
      // 如果已有选中的学生，重新加载量表
      if (selectedStudents.value.length > 0) {
        await loadApplicableCriteria();
      } else {
        applicableCriteria.value = [];
      }
    }
  );
  
  // 提取量表加载逻辑为独立函数
  const loadApplicableCriteria = async () => {
    if (selectedStudents.value.length === 0) {
      applicableCriteria.value = [];
      return;
    }
    
    criteriaLoading.value = true;
    try {
      const studentIds = selectedStudents.value.map(s => s.id);
      const { data } = await request('/evaluation/customCriterion/findApplicableForStudents', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data: { 
          studentIds,
          type: criterionType.value
        }
      });
      applicableCriteria.value = data || [];
      
      // 如果当前选中的量表不适用，清空选择
      if (formData.value.criterionId && 
          !applicableCriteria.value.find(c => c.id == formData.value.criterionId)) {
        formData.value.criterionId = '';
      }
    } catch (error) {
      Message.error('加载适用量表失败');
      applicableCriteria.value = [];
    } finally {
      criteriaLoading.value = false;
    }
  };
</script>

<template>
  <a-modal
    v-model:visible="visible"
    title="新建评估"
    :width="700"
    ok-text="开始评估"
    :ok-button-props="{ disabled: !formData.criterionId || selectedStudents.length === 0 }"
    :on-before-ok="handleGoStart"
    @open="handleOpen"
    @cancel="handleClose"
  >
    <a-form ref="formRef" :model="formData" :auto-label-width="true" size="small">
      <div class="flex gap-4">
        <a-form-item label="学期" field="period" :rules="[{ required: true, message: '请正确选择学期' }]">
          <a-select v-model="formData.period" placeholder="请正确选择学期">
            <!-- :options="periods"-->
            <a-option v-for="period in periods" :key="period" :value="period">
              <span :class="{ 'text-green-600': period.includes('春'), 'text-red-600': !period.includes('春') }">
                {{ period }}
              </span>
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="评估日期">
          <a-date-picker v-model="formData.evaluationDate" />
        </a-form-item>
      </div>
      <a-form-item label="评估类型">
        <a-select v-model="criterionType" class="w-24">
          <a-option value="Criterion">量表</a-option>
          <a-option value="Diagnosis">诊断</a-option>
        </a-select>
      </a-form-item>
      <a-divider :margin="5" />
      <a-form-item label="学生" class="mt-4">
        <div class="flex flex-col gap-2 flex-1">
          <student-select
            :use-grade-class-filter="true"
            :default-query-params="studentDefaultQueryParams"
            :select-all-visible="true"
            @change="handleStudentChange"
            @select-all="handleSelectAll"
            @cancel="handleCancel"
          />
          <a-table :data="selectedStudents" size="mini" :bordered="false" style="user-select: none" :pagination="false">
            <template #columns>
              <a-table-column title="班级">
                <template #cell="{ record, rowIndex }">
                  <div
                    class="flex justify-start items-center space-x-2"
                    :draggable="true"
                    @dragstart="handleDragStart(rowIndex)"
                    @dragenter="handleDragEnter(rowIndex)"
                    @dragover="handleDragOver($event, rowIndex)"
                    @dragend="handleDragEnd"
                  >
                    <div class="w-[20px] text-gray-600">
                      <icon-drag-dot-vertical size="12" class="hover:text-blue-600 cursor-move" />
                    </div>
                    <span>{{ record.gradeClass?.name || '-' }}</span>
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="姓名" data-index="name" />
              <a-table-column title="性别" data-index="gender" />
              <a-table-column title="年龄" data-index="age" />
              <a-table-column title="残疾类别" data-index="disorders" />
              <a-table-column title="操作">
                <template #cell="{ record }">
                  <a-button type="text" size="mini" status="danger" @click="() => handleDeleteStudent(record)">
                    删除
                  </a-button>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </a-form-item>
      <a-divider :margin="5" />
      <a-form-item label="评估量表" class="mt-4">
        <a-select 
          v-model="formData.criterionId"
          :disabled="selectedStudents.length === 0"
          :loading="criteriaLoading"
          :placeholder="selectedStudents.length === 0 ? '请先选择学生' : '请选择评估量表'"
          class="w-full"
        >
          <a-option 
            v-for="criterion in applicableCriteria" 
            :key="criterion.id" 
            :value="criterion.id"
          >
            {{ criterion.name }}
          </a-option>
        </a-select>
        <div v-if="selectedStudents.length > 0 && applicableCriteria.length === 0 && !criteriaLoading" 
             class="text-orange-500 text-sm mt-1">
          没有找到适用于所选学生的量表
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="scss"></style>
