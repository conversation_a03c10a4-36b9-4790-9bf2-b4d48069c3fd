<script setup lang="ts">
import {ref, onMounted, nextTick, watch} from 'vue'
import {onLoad, onPullDownRefresh, onShow, onHide} from '@dcloudio/uni-app'
import {PROJECT_URLS} from '@/common/env';
import {marked} from "marked";

// 响应式数据
const isReply = ref(false)
const isLoading = ref(false)
const msg = ref('')
const chatCatch = ref<any[]>([])
const sessionId = ref('')
const chatResponse = ref('')
const scrollViewId = ref('chat-scroll-view')
const isKeyboardVisible = ref(false)
const requestController = ref<any>(null)


// 聊天历史管理
const loadChatHistory = () => {
  try {
    const history = uni.getStorageSync(`chat_history_${sessionId.value || 'default'}`)
    return history ? JSON.parse(history) : []
  } catch (error) {
    console.error('加载聊天历史失败:', error)
    return []
  }
}

const saveChatHistory = () => {
  try {
    uni.setStorageSync(`chat_history_${sessionId.value || 'default'}`, JSON.stringify(chatCatch.value))
  } catch (error) {
    console.error('保存聊天历史失败:', error)
  }
}

// 重置状态
const reset = () => {
  isReply.value = false
}

// 滚动到底部
const handleScrollToBottom = async () => {
  await nextTick()
  scrollViewId.value = `scroll-anchor-${Date.now()}`
}

// 复制消息
const handleCopy = (message: string) => {
  uni.setClipboardData({
    data: message,
    success: () => {
      uni.showToast({
        title: '已复制',
        icon: 'success',
        duration: 2000
      })
    }
  })
}

// 清空聊天历史
const clearChatHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有聊天记录吗？',
    success: (res) => {
      if (res.confirm) {
        chatCatch.value = []
        try {
          uni.removeStorageSync(`chat_history_${sessionId.value || 'default'}`)
          uni.removeStorageSync('behavior_chat_sessionId')
        } catch (error) {
          console.error('清空历史记录失败:', error)
        }
        reset()
        uni.showToast({
          title: '已清空',
          icon: 'success',
          duration: 2000
        })
      }
    }
  })
}

// 发送消息
const handleSend = () => {
  if (!msg.value.trim()) {
    uni.showToast({
      title: '发送消息不能为空',
      icon: 'none',
      duration: 2000
    })
    return
  }

  isReply.value = true
  const userMessage = msg.value

  chatCatch.value.push({role: 'user', msg: userMessage})
  chatCatch.value.push({role: 'robot', msg: ''})
  msg.value = ''
  requestStreamChat(userMessage)
  saveChatHistory()
  handleScrollToBottom()
}

// 模拟流式响应显示
const simulateStreamDisplay = (fullText: string) => {
  if (!fullText) return

  chatResponse.value = ''
  let currentIndex = 0

  const displayNextChunk = () => {
    if (currentIndex < fullText.length && isReply.value) {
      const chunkSize = Math.min(Math.floor(Math.random() * 3) + 1, fullText.length - currentIndex)
      const chunk = fullText.slice(currentIndex, currentIndex + chunkSize)
      chatResponse.value += chunk
      currentIndex += chunkSize

      if (chatCatch.value.length > 0) {
        chatCatch.value[chatCatch.value.length - 1].msg = chatResponse.value
      }

      handleScrollToBottom()
      setTimeout(displayNextChunk, Math.random() * 50 + 30)
    } else {
      isReply.value = false
      saveChatHistory()
      reset()
    }
  }

  displayNextChunk()
}

// 流式聊天API请求
const requestStreamChat = async (message: string) => {
  try {
    chatResponse.value = ''
    const token = uni.getStorageSync('token') || ''

    let requestUrl = `${PROJECT_URLS.MAIN_PROJECT_API}/resourceRoom/behaviorAnalysis/chat/streamCall?message=${encodeURIComponent(message)}`

    if (sessionId.value && sessionId.value !== '') {
      requestUrl += `&sessionId=${encodeURIComponent(sessionId.value)}`
    }

    requestController.value = {
      abort: () => {
        isReply.value = false
        console.log('请求已取消')
      }
    }

    const requestTask = uni.request({
      url: requestUrl,
      method: 'PUT',
      header: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Authorization': `Bearer ${token}`,
        'LoginSource': 'WeApp',
      },
      success: (res: any) => {
        if (res.statusCode === 200 && res.data) {
          let fullText = ''
          let newSessionId = ''

          if (typeof res.data === 'string') {
            const parsed = parseStreamResponse(res.data)
            fullText = parsed.text
            newSessionId = parsed.sessionId
          } else if (res.data?.output) {
            fullText = res.data?.output.text || ''
            newSessionId = res.data?.output.sessionId || ''
          } else {
            const responseText = JSON.stringify(res.data)
            const parsed = parseStreamResponse(responseText)
            fullText = parsed.text
            newSessionId = parsed.sessionId
          }

          if (newSessionId) {
            sessionId.value = newSessionId
          }

          if (fullText) {
            simulateStreamDisplay(fullText)
          } else {
            isReply.value = false
            reset()
          }
        } else {
          throw new Error(`请求失败: ${res.statusCode}`)
        }
      },
      fail: (error) => {
        isReply.value = false
        if (chatCatch.value.length > 0 && !chatCatch.value[chatCatch.value.length - 1].msg) {
          chatCatch.value.pop()
        }
      }
    })

    requestController.value.abort = () => {
      requestTask.abort()
      isReply.value = false
    }

  } catch (error) {
    isReply.value = false
    if (chatCatch.value.length > 0 && !chatCatch.value[chatCatch.value.length - 1].msg) {
      chatCatch.value.pop()
    }
  }
}

// 解析流式响应
const parseStreamResponse = (responseText: string) => {
  let fullText = ''
  let sessionId = ''

  try {
    const lines = responseText.split('\n')
    for (const line of lines) {
      if (line.startsWith('data:')) {
        const jsonText = line.slice(5).trim()
        try {
          const json = JSON.parse(jsonText)
          const text = json.output?.text || ''
          const newSessionId = json.output?.sessionId || ''

          if (newSessionId) {
            sessionId = newSessionId
          }
          if (text) {
            fullText += text
          }
        } catch (err) {
          console.error('JSON解析失败：', jsonText, err)
        }
      }
    }
  } catch (error) {
    console.error('解析响应失败:', error)
  }

  return { text: fullText, sessionId }
}

// 键盘事件处理
const handleKeyboardShow = () => {
  isKeyboardVisible.value = true
  nextTick(() => {
    handleScrollToBottom()
  })
}

const handleKeyboardHide = () => {
  isKeyboardVisible.value = false
}

const handleInputFocus = () => {
  handleKeyboardShow()
}

const handleInputBlur = () => {
  handleKeyboardHide()
}

onLoad(async (options) => {
  const token = options?.token || uni.getStorageSync('token')
  const userId = options?.userId || uni.getStorageSync('userId')

  if (token) {
    uni.setStorageSync('token', token)
  }
  if (userId) {
    uni.setStorageSync('userId', userId)
  }

  try {
    sessionId.value = uni.getStorageSync('behavior_chat_sessionId') || ''
  } catch (error) {
    console.error('加载session ID失败:', error)
  }
})

onMounted(() => {
  chatCatch.value = loadChatHistory()
  nextTick(() => {
    handleScrollToBottom()
  })
})

onShow(() => {
  chatCatch.value = loadChatHistory()
})

onHide(() => {
  saveChatHistory()
})

onPullDownRefresh(() => {
  chatCatch.value = loadChatHistory()
  uni.stopPullDownRefresh()
})

watch(
    () => sessionId.value,
    (newVal) => {
      if (newVal) {
        try {
          uni.setStorageSync('behavior_chat_sessionId', newVal)
        } catch (error) {
          console.error('保存session ID失败:', error)
        }
      }
    },
    {deep: true}
)

watch(
    () => chatResponse.value,
    async (newVal) => {
      if (newVal && chatCatch.value.length > 0) {
        chatCatch.value[chatCatch.value.length - 1].msg = newVal
        await handleScrollToBottom()
      }
    },
    {deep: true, immediate: true}
)
</script>

<template>
  <view class="h-screen flex flex-col bg-gray-100">
    <!-- 聊天消息区域 -->
    <scroll-view
        :scroll-y="true"
        class="flex-1 p-4 overflow-hidden"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
    >
      <view class="space-y-4 ">
        <view
            v-for="(message, index) in chatCatch"
            :key="index"
            :class="[
            'flex',
            message.role === 'user' ? 'justify-end' : 'justify-start'
          ]"
        >
          <!-- 机器人消息 -->
          <view v-if="message.role === 'robot'" class="max-w-[100%] " style="{line-height: 25px}">
            <view
                v-if="!message?.msg && index === chatCatch.length - 1 && isReply"
                  class="bg-white p-4 rounded-lg shadow-sm border border-gray-200"
            >
              <view class="text-gray-600">AI正在思考中...</view>
              <view class="flex space-x-2 mt-2">
                <view class="w-2 h-2 rounded-full bg-blue-500 animate-pulse"></view>
                <view class="w-2 h-2 rounded-full bg-blue-500 animate-pulse delay-100"></view>
                <view class="w-2 h-2 rounded-full bg-blue-500 animate-pulse delay-200"></view>
              </view>
            </view>
            <view v-else class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
              <rich-text class="text-gray-800 " :nodes="marked(message.msg||'')"></rich-text>
              <view class="flex justify-end mt-2">
                <view
                    class="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded"
                    @tap="handleCopy(message.msg)"
                >
                  复制
                </view>
              </view>
            </view>
          </view>

          <!-- 用户消息 -->
          <view v-else class="max-w-[80%]">
            <view class="bg-blue-500 text-white p-4 rounded-lg shadow">
              <rich-text class="" :nodes="marked(message.msg||'')"></rich-text>
            </view>
          </view>
        </view>
      </view>

      <!-- 滚动锚点 -->
      <view :id="scrollViewId" class="h-1"></view>
    </scroll-view>

    <!-- 底部工具栏 -->
    <view class="bg-white border-t border-gray-200 p-4">
      <textarea
          v-model="msg"
          class="w-full min-h-[80rpx] max-h-[200rpx] p-3 bg-gray-50 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none"
          placeholder="问一问AI..."
          :disabled="isReply"
          :maxlength="500"
          :auto-height="true"
          :show-confirm-bar="false"
          :adjust-position="true"
          :hold-keyboard="true"
          :cursor-spacing="30"
          @confirm="handleSend"
          @focus="handleInputFocus"
          @blur="handleInputBlur"
      />

      <view class="flex justify-between items-center mt-3">
        <view class="text-sm text-gray-500">{{ msg.length }}/500</view>

        <view class="flex space-x-2">
          <view
              class="px-4 py-2 text-sm text-gray-600 bg-gray-100 rounded-full"
              @tap="clearChatHistory"
          >
            清空记录
          </view>

          <view
              :class="[
              'px-4 py-2 text-sm text-white rounded-full',
              (!msg.trim() || isReply) ? 'bg-gray-400' : 'bg-blue-500',
              isReply ? 'bg-red-500' : ''
            ]"
              @tap="handleSend"
          >
            {{ isReply ? '停止' : '发送' }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
"style": {
"navigationBarTitleText": "AI助手",
"enablePullDownRefresh": true,
"navigationBarBackgroundColor": "#1989fa",
"navigationBarTextStyle": "white",
"backgroundColor": "#f5f5f5",
"disableScroll": true
}
}
</route>