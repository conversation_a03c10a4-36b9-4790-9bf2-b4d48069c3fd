<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
  currentRecord: {
    type: Object,
    required: true,
  },
  schema: {
    type: Object,
    required: false,
  },
});

const currentRecord = ref({ ...props.currentRecord });
const getParentSatisfactionValue = (value: number | undefined, type: 'content' | 'timing' | 'attitude') => {
  if (value === undefined) return '';
  switch (type) {
    case 'content':
    case 'timing':
      return value === 1 ? '合理' : '不合理';
    case 'attitude':
      return value === 1 ? '良好' : '欠佳';
    default:
      return '';
  }
};

const getAttitudeComment = (comment: string | undefined) => {
  return comment || '无';
};
const tdTitle = 'border border-gray-400 px-3 py-2 bg-gray-50 font-medium text-gray-700 align-top whitespace-nowrap w-auto';
const tdContent = 'border border-gray-400 px-3 py-2 text-gray-800 leading-6 align-top';
const tdColspan = 'border border-gray-400 px-4 py-3 text-gray-800 leading-6 align-top';
</script>

<template>
  <div class="w-[210mm] min-h-[297mm] p-6 text-[13px]">
    <table class="w-full border border-gray-400 table-auto text-sm">
      <!-- 表头 -->
      <tr>
        <td colspan="4" class="border border-gray-400">
          <div class="text-center text-lg font-bold py-3 tracking-wide">“送教上门”登记卡</div>
        </td>
      </tr>

      <!-- 第一行 -->
      <tr>
        <td :class="tdTitle">送教日期</td>
        <td :class="tdContent">{{ currentRecord.date }}</td>
        <td :class="tdTitle">送教教师</td>
        <td :class="tdContent">{{ currentRecord.teacher }}</td>
      </tr>

      <!-- 教学目标 -->
      <tr>
        <td :class="tdTitle">教学目标</td>
        <td :class="tdColspan" colspan="3">{{ currentRecord?.additionalData?.teachingTarget }}</td>
      </tr>
      <tr>
        <td :class="tdTitle">教具准备</td>
        <td :class="tdColspan" colspan="3">{{ currentRecord?.additionalData?.teachingAids }}</td>
      </tr>
      <!-- 送教过程 -->
      <tr>
        <td :class="tdTitle">送教过程</td>
        <td :class="tdColspan" colspan="3">{{ currentRecord?.additionalData?.teachingProcess }}</td>
      </tr>

      <!-- 学生表现 -->
      <tr>
        <td :class="tdTitle">学生表现</td>
        <td :class="tdColspan" colspan="3">{{ currentRecord?.additionalData?.studentPerformance }}</td>
      </tr>

      <!-- 指导意见 -->
      <tr>
        <td :class="tdTitle">
          家庭教育指导意见<br />
          <span class="text-xs text-gray-500">(针对情况对该生服务的建议)</span>
        </td>
        <td :class="tdColspan" colspan="3">{{ currentRecord?.additionalData?.opinion }}</td>
      </tr>

      <!-- 家长满意度 -->
      <tr>
        <td :class="tdTitle">家长满意度</td>
        <td :class="tdColspan" colspan="3">
          <div class="mb-3">本学期送教共计______次，本次为第______次</div>

          <div class="flex items-start flex-col mb-4">
            <div class="">
              <span class="whitespace-nowrap mr-2">送教时间安排：</span>
              <span>{{ getParentSatisfactionValue(currentRecord.additionalData?.parentSatisfaction?.timing, 'timing') }}</span>
            </div>
            <div class="flex items-center">
              <span class="whitespace-nowrap mr-2">送教内容安排：</span>
              <span>{{ getParentSatisfactionValue(currentRecord.additionalData?.parentSatisfaction?.content, 'content') }}</span>
            </div>
            <div class="flex items-center">
              <span class="whitespace-nowrap mr-2">教师教学态度：</span>
              <span>{{ getParentSatisfactionValue(currentRecord.additionalData?.parentSatisfaction?.attitude, 'attitude') }}</span>
            </div>
          </div>

          <div class="flex items-start mb-6">
            <span class="mr-2 whitespace-nowrap">其它：</span>
            <span class="flex-1   border-gray-500 min-h-[24px]">
              {{ getAttitudeComment(currentRecord.additionalData?.parentSatisfaction?.attitudeComment) }}
            </span>
          </div>

          <div class="text-right mt-6">家长签字：________________</div>
        </td>
      </tr>
    </table>
  </div>
</template>

<style scoped>
@media print {
  @page {
    size: A4;
    margin: 0;
  }
  body {
    margin: 0;
    padding: 0;
  }
}
</style>
