<script setup lang="ts">
import { computed, inject, PropType, ref } from 'vue';

const merge = inject('merge');
const cloneDeep = inject('cloneDeep');
const debouncedWatch = inject('debouncedWatch');
const request = inject('request');
const PROJECT_URLS = inject('PROJECT_URLS');
const uploader = inject('uploader');

const props = defineProps({
  record: {
    type: Object as PropType<any>,
    required: true,
  },
  sendEducationPlan: {
    type: Object as PropType<any>,
    required: true,
  },
  tableRef: {
    type: Object as PropType<HTMLElement | null>,
    default: () => null,
  },
});

const userInfo = inject('userInfo');

const emit = defineEmits(['update:record']);

const raw = computed(() => {
  const result = merge(
      {
        date: new Date(),
        teacher: userInfo?.name,
        school: userInfo?.branchOffice?.name,
        sendEducationPlan: {
          id: props.sendEducationPlan?.id,
        },
        additionalData: {
          attachments:[]
        },
      },
      props.record,
  );
  result.sendEducationPlan = {
    id: result.sendEducationPlan.id,
    student: {
      id: props.sendEducationPlan.student.id,
      fusionSchool: { name: props.sendEducationPlan.student.fusionSchool.name },
    },
  };
  result.additionalData.parentSatisfaction = result.additionalData.parentSatisfaction || {};
  return cloneDeep(result);
});

const modelValue = ref(raw);

debouncedWatch(
    () => modelValue.value,
    (record) => {
      emit('update:record', record);
    },
    { deep: true, debounce: 500 },
);

const handleSubmit = async () => {
  const sendEduPlanRecord = {
    ...modelValue.value,
  };

  if (sendEduPlanRecord.id) {
    await request(`/resourceRoom/sendEducationRecord/${sendEduPlanRecord.id}`, {
      method: 'PUT',
      data: sendEduPlanRecord,
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
  } else {
    await request('/resourceRoom/sendEducationRecord', {
      method: 'POST',
      data: sendEduPlanRecord,
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
  }
  setTimeout(async () => {
    await props.tableRef?.loadData();
  }, 50);
};
defineExpose({ handleSubmit });
</script>

<template>
  <div v-if="modelValue" class="w-full">
    <div class="text-center text-lg pb-4"> 四川省成都市双流区残疾儿童“送教上门”登记卡 </div>
    <a-form size="mini" :model="modelValue" auto-label-width>
      <a-row :gutter="20">
        <a-col :span="12">
          <a-form-item label="送教日期">
            <a-date-picker v-model="modelValue.date" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="送教教师">
            <a-input v-model="modelValue.teacher" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item label="教学目标">
        <a-textarea v-model="modelValue.additionalData.teachingTarget" />
      </a-form-item>
      <a-form-item label="教具准备">
        <a-textarea v-model="modelValue.additionalData.teachingAids" />
      </a-form-item>
      <a-form-item label="教学过程">
        <a-textarea v-model="modelValue.additionalData.teachingProcess" />
      </a-form-item>
      <a-form-item label="学生表现">
        <a-textarea v-model="modelValue.additionalData.studentPerformance" />
      </a-form-item>
      <a-form-item label="家庭教育指导意见">
        <a-textarea v-model="modelValue.additionalData.opinion" placeholder="针对情况对该生服务的建议" />
      </a-form-item>
      <a-divider>家长满意度</a-divider>
      <a-form-item label="送教时间安排">
        <a-radio-group v-model="modelValue.additionalData.parentSatisfaction.timing">
          <a-radio :value="1">合理</a-radio>
          <a-radio :value="2">不合理</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="送教内容安排">
        <a-radio-group v-model="modelValue.additionalData.parentSatisfaction.content">
          <a-radio :value="1">合理</a-radio>
          <a-radio :value="2">不合理</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="教师教学态度">
        <a-space class="items-center">
          <a-radio-group v-model="modelValue.additionalData.parentSatisfaction.attitude">
            <a-radio :value="1">良好</a-radio>
            <a-radio :value="2">欠佳</a-radio>
          </a-radio-group>
          <a-input v-model="modelValue.additionalData.parentSatisfaction.attitudeComment" placeholder="其他意见">
            <template #prefix> 其他 </template>
          </a-input>
        </a-space>
      </a-form-item>
      <a-form-item label="送教照片">
        <uploader
            v-model="modelValue.additionalData.attachments"
            sub-folder="poster/attachments"
            :max-size="1024 * 4"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped lang="scss"></style>
